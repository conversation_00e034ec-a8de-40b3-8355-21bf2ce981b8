<template>
  <div class="zone p-12 mt-8 bg-white">
    <div class="brand__item" v-for="(item, index) in list" :key="index" @click="jumpTo(item)">
      <van-image :src="item.src" class="brand__item-img h-86 w-full" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router"

import coudan from '@/assets/home/<USER>'
import zone from '@/assets/home/<USER>'
import { useHomeStore } from '@/stores/home'

const list = [{ src: zone, type: 'zone', name: '精品礼包' }, { src: coudan, type: 'collection', name: '凑单专区' }]

const homeStore = useHomeStore()
const homeConfig = homeStore.configInfo.homeConfig || {}
const bestCategorys = homeConfig.bestProductCategorys || []
console.log('bestCategorys=======', bestCategorys)

list.forEach(item => {
  const cate = bestCategorys.find(cate => cate.name === item.name)
  if(cate) {
    item.categoryId = cate.id
    item.categoryLevel = cate.level
  }
})

const router = useRouter()
const jumpTo = (item: object) => {
  const query = {
    name: item.name
  }
  const pname = `categoryId${ item.categoryLevel }`
  query[pname] = item.categoryId
  router.push({
    path: '/home/<USER>',
    query: query
  })
}
</script>

<style scoped lang="scss">
.zone {
  .brand__item {
    font-size: 0;
    & + .brand__item {
      margin-top: 8px;
    }
  }
}
</style>
