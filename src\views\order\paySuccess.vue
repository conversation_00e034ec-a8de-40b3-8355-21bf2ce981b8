<template>
  <div>
    <NavBar title=""></NavBar>
    <div class="flex-center flex-col">
      <img :src="paySuccess" class="mt-40 size-96" />
      <div class="mt-20 text-20 font-600 text-black lh-24">订单支付成功</div>
      <div class="text-[#999999] text-12 mt-10">订单号：{{ orderNo }}</div>
      <div class="mt-40 flex flex-col items-center">
        <van-button round class="w-180 btn-primary" @click="toOrder">查看订单</van-button>
        <van-button round plain color="#999999" class="w-180 !mt-10" replace to="/home">返回首页</van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'PaySuccess'
})
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import paySuccess from '@/assets/order/paySuccess.png'
import NavBar from '@/components/navbar/index.vue'

const router = useRouter()
const route = useRoute()
const query = route.query

const orderNo = computed(() => query.no)

const toOrder = () => {
  if(query.oType !== '1' && orderNo.value) {
    router.replace({
      path: '/order/orderDetail',
      query: {
        no: orderNo.value
      }
    })
    return
  }
  router.replace('/order/index')
}
</script>

<style scoped lang="scss"></style>
