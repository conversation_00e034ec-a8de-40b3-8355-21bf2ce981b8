<template>
  <div class="pb-90 position-relative">
    <div class="header flex-center flex-col">
      <div class="top-bg"></div>
      <div class="bottom-bg">
        <img :src="logo" class="avatar w-65 h-65 rounded-[50%]" />
        <div class="name font-600 text-14" style="color: #000">{{ configData.title }}（{{ userInfo.nickname }}）</div>
        <div class="mask-wrap">
          <div class="w-100" v-for="item in user" :key="item.scoreType">
            <div class="text-12">{{ item.scoreType == 0 ? '福利积分' : '扶贫积分' }}</div>
            <div class="text-22 font-600 mt-4">{{ item.availableAmount }}</div>
          </div>
          <div class="bg-white color-orange w-92 h-34 rounded-30 lh-34 text-center" @click="toPoints">我的积分</div>
        </div>
        <div class="my-order">
          <div class="order-title">
            <div class="text-black font-600">我的订单</div>
            <div class="color-#847D7D text-12" @click="toOrder">全部订单 ></div>
          </div>
          <div class="flex items-center justify-around mt-16">
            <div v-for="(item,index) in orderList" :key="index" class="order-item" @click="toOrderList(item)">
              <van-badge v-if="item.num > 0" :content="item.num">
                <img :src="item.img" class="size-32" />
              </van-badge>
              <img v-else :src="item.img" class="size-32" />
              <div class="text-12 color-#6C6C6C">{{ item.title }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <van-cell-group class="group-list">
      <van-cell to="/profile/password" title="修改密码" is-link />
      <van-cell to="/collect/index" class="first-item" title="我的收藏" is-link />
      <van-cell to="/address/index" title="收货地址" is-link />
      <van-cell to="/feedback/index" title="意见反馈" is-link />
      <van-cell to="/customerService/index" class="last-item" title="客户服务" is-link />
    </van-cell-group>
    <div class="logout flex-center" @click="logout">退出登录</div>
  </div>
</template>

<script setup lang="ts">
import { reactive,ref } from 'vue'
import { useRouter } from 'vue-router'

import { getOrderNum } from '@/api/order'
import { userAccount } from '@/api/user'
import logo from '@/assets/login/logo.png'
import cancel from '@/assets/mine/cancel.png'
import pay from '@/assets/mine/pay.png'
import receive from '@/assets/mine/receive.png'
import send from '@/assets/mine/send.png'
import { useHomeStore } from '@/stores/home'
import { useUserStore } from '@/stores/user'
import { toLogout } from '@/utils/ssoUtils'
import { debug } from 'node:console'

const homeStore = useHomeStore()
const userStore = useUserStore()

const configData = homeStore.configInfo.configData
const userInfo = userStore.userInfo

const orderList = reactive([
  {
    img: pay,
    title: '待确认',
    num: 0,
    status: 1
  },
  {
    img: send,
    title: '待发货',
    num: 0,
    status: 2
  },
  {
    img: receive,
    title: '待收货',
    num: 0,
    status: 45
  },
  {
    img: cancel,
    title: '售后中',
    num: 0,
    status: 6
  }
])

const router = useRouter()
const logout = () => {
  userStore.clean4Logout()
  toLogout()
}

const toPoints = () => {
  router.push('/points/index')
}

const toOrder = () => {
  router.push('/order/index')
}

const toOrderList = (item) => {
  router.push({
    path: '/order/index',
    query: {
      status: item.status
    }
  })
}

const user = ref({})
const getUserAccount = async () => {
  const data = await userAccount()
  user.value = data || [
        {
            "scoreType": 0,
            "availableAmount": 0.00
        },
        {
            "scoreType": 1,
            "availableAmount": 0.00
        }
    ]
}

const getOrderNumList = async () => {
  const data = await getOrderNum()
  const list = data || []
  orderList.forEach(item => {
    const statusObj = list.find(x => x.status == item.status)
    item.num = statusObj?.count || 0
  })
}

getUserAccount()
getOrderNumList()
</script>

<style scoped lang="scss">
.header {
  .top-bg {
    width: 100%;
    height: 264px;
    &:after {
      content: '';
      display: block;
      width: 100%;
      height: 100%;
      background: url('@/assets/mine/wuda-home.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  .bottom-bg {
    position: relative;
    width: 100%;
    height: 290px;
    background-color: #fff;
    top: -95px;
    margin-bottom: -85px;
    padding: 0 15px;
  }
  .avatar {
    position: absolute;
    top: -24px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
  }
  .name {
    margin-top: 40px;
    text-align: center;
  }
  .mask-wrap {
    margin-top: 16px;
    width: 100%;
    height: 85px;
    padding: 20px 24px;
    color: #fff;
    background-color: orange;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 10px;
    background: url('@/assets/mine/mask-bg.png') no-repeat;
    background-size: 100% 100%;
  }
  .my-order {
    margin-top: 34px;
    .order-title {
      width: 100%;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .order-item {
      width: 36px;
    }
  }
}
.group-list {
  position: relative;
  border-radius: 10px;
  :deep(.van-cell__title) {
    font-weight: 600;
  }
  .first-item {
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
  }
  .last-item {
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
  }
}
.logout {
  margin: 20px 0 0;
  font-weight: 600;
  font-size: 14px;
  color: $primary-color;
}
</style>
