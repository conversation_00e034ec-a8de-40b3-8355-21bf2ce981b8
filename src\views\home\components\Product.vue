<template>
    <div class="product__grid mt-15">
      <div class="title">
      <div>{{ title }}</div>
      <!-- <div><van-button round icon="replay" size="mini" @click="refresh"></van-button></div> -->
    </div>
    <div class="product-body">
      <div class="mt-5 mb-40">
        <van-list
          v-model:loading="loading"
          loading-text="加载中..."
          :finished="finished"
          :finished-text="productList.length ? '没有更多了' : ''"
          @load="onLoad"
          :immediate-check="false"
        >
          <van-row>
            <van-col span="12" v-for="(child, idx) in productList" :key="idx">
              <div class="flex py-10 px-10 flex-vertical round-card" @click="toDetail(child)">
                <div class="size-117 position-relative">
                  <van-image height="100%" fit="contain" :src="child.imageUrl" />
                  <img v-if="isOutStock(child.stockStateType)" :src="outStock" class="out-stock size-72">
                </div>
                <div class="ml-10 flex flex-col py-5 flex-1" style="width:100%;">
                  <div class="font-600 text-14 text-black ellipsis-2 lh-16" style="min-height: 32px;">
                    {{ child.skuName }}
                  </div>
                  <div v-if="child.salePrice != -1" class="flex items-end mt-30 font-600 text-20 color-primary">
                    <div>
                      <span class="unit text-12 lh-12">¥</span>
                      <span class="lh-18">{{ formatMoney(child.salePrice) }}</span>
                    </div>
                    <div class="ml-10 m-price" v-if="enableMarketPrice()">
                      <span class="unit text-12 lh-12">¥</span>
                      <span class="lh-18">{{ child.marketPrice }}</span>
                    </div>
                  </div>
                  <div v-else class="flex items-end mt-30 font-500 text-12 color-#989898">
                    <div>
                      <span class="lh-18">登录后查看价格</span>
                    </div>
                  </div>
                <div class="flex items-center justify-between mt-14 lh-12">
                  <div class="text-12 text-[#666666]" v-if="enableSupName()">{{child.supplierName}}</div>
                  <!-- <div class="text-12 text-[#666666]">销量：999+</div> -->
                  <div></div>
                  <div class="flex items-center py-5 pl-10 h-26" @click.stop="addCart(child)">
                    <van-icon name="cart-o" :color="(!isStockValid(child.stockStateType) || child.skuState != 1) ? '#999999' : '#F22E00'" class="text-16 mr-7" />
                    <span class="text-12 color-primary" :class="{'c-999': !isStockValid(child.stockStateType) || child.skuState != 1}">加入购物车</span>
                  </div>
                </div>
              </div>
            </div>
          </van-col>
        </van-row>
      </van-list>
      <EmptyBox v-if="inited && !productList.length" style="height: 400px;"/>
      <van-back-top class="goods-custom">
        <img class="size-36" :src="backTop" />
      </van-back-top>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { showToast } from 'vant'

import { formatMoney } from '@/utils/base'
defineOptions({
  name: 'Product'
})

import { ref,watch } from 'vue'
import { useRouter } from 'vue-router'

import { goodsSearchPageList } from '@/api/classify'
import { addCartApi } from '@/api/shoppingCart'
import backTop from '@/assets/goods/backTop.png'
import outStock from '@/assets/goods/outStock.png'
import EmptyBox from '@/components/emptyBox/index.vue'
import { useUserStore } from '@/stores/user'
import { enableMarketPrice, enableSupName, isOutStock,isStockValid } from '@/utils/productUtils'

const props = defineProps({
  title: {
    type: String,
    default: '热销商品'
  },
  tagIds: {
    type: String,
    default: ''
  }
})

const router = useRouter()
const productList = ref([])
const loading = ref(false)
const inited = ref(false)
const finished = ref(false)
const userStore = useUserStore()
const address = userStore.defaultAddress
let areaIds = `${ address.provinceId },${ address.cityId },${ address.countyId }`
if (address.townId) {
  areaIds += `,${ address.townId }`
}

const params = ref({
  areaIds,
  pageIndex: 1,
  pageSize: 10,
  sortType: 1
})

const loadProductList = async () => {
  loading.value =true
  params.value.tagIds = props.tagIds
  const data = await goodsSearchPageList(params.value)
  if (data.pageResult && data.pageResult.list) {
    productList.value = productList.value.concat(data.pageResult.list)
  } else {
    productList.value = []
  }
  loading.value = false
  inited.value = true
  finished.value = productList.value.length >= data.pageResult.total
}

const onLoad = () => {
  params.value.pageIndex++
  loadProductList()
}

const toDetail = (data: any) => {
  router.push({
    path: '/goods/detail',
    query: {
      supplierId: data.supplierId,
      skuId: data.skuId
    }
  })
}

// 加入购物车
const addCart = async (item: any) => {
  if(!item.skuId) {
    return
  }
  if (!isStockValid(item.stockStateType) || item.skuState != 1) {
    return
  }
  const params = {
    area: {
      provinceId: address.provinceId,
      cityId: address.cityId,
      countyId: address.countyId,
      townId: ''
    },
    count: 1,
    skuId: item.skuId,
    supplierId: item.supplierId
  }
  if (address.townId) {
    params.area.townId = address.townId
  }
  const data = await addCartApi(params)
  if (data) {
    showToast('加入购物车成功！')
    userStore.loadCartCount()
  }
}

const refresh = () => {
  inited.value = false
  params.value.pageIndex = 1
  productList.value = []
  loadProductList()
}

refresh()

watch(() => userStore.userInfo,
  () => {
    refresh()
  },
  { deep: true})

defineExpose({ refresh })

</script>

<style scoped lang="scss">
.product__grid {
  .title {
    font-weight: 500;
    font-size: 1.1em;
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .flex-vertical {
    flex-direction: column;
    align-items: center;
  }
  .round-card {
    border-radius: 5px;
    background-color: white;
    margin: 5px;
  }
  .out-stock {
    position: absolute;
    left: 22.5px;
    top: 22.5px;
  }
  .m-price {
    font-size:0.65em;
    color:#989898;
    text-decoration: line-through;
  }
}

</style>
<style lang="scss">
.goods-custom {
  box-shadow: initial;
  background-color: initial;
  width: 36px;
  height: 36px;
  right: 13px;
  bottom: 100px;
}
</style>
