import { sm2 } from 'sm-crypto'

const key1 = 'accessToken'
const key2 = 'refreshToken'
const localStorage = window.localStorage

export const setToken = (data:any) => {
  localStorage.setItem(key1, data.accessToken)
  localStorage.setItem(key2, data.refreshToken)
}

export const getToken = () => localStorage.getItem(key1)

export const getRefreshToken = () => localStorage.getItem(key2)

export const removeToken = () => {
  localStorage.removeItem(key1)
  localStorage.removeItem(key2)
}

export const setCookie = (key: string, value: string) => {
  localStorage.setItem(key, value)
}

export const getCookie = (key: string) => localStorage.getItem(key)

export const encryptSm2 = (data: string) => {
  const cipherMode = 1
  const publicKey = "04090D509CE405A6E0E9809547BA179BFB990DFECA4EE164152C41DC325E48C93BB4B82222F43AE168E887CFCC3686BF7B5F22E86D415514A6F1FF015D7BE20FA0"
  return sm2.doEncrypt(data, publicKey, cipherMode)
}

export const decryptSm2 = (data: string, privateKey: string) => {
  const cipherMode = 1
  return sm2.doDecrypt(data, privateKey, cipherMode)
}
