/**
 * 订单状态
 */
export const ORDER_STATUS_LIST = [
  { label: '待确认', value: 1 },
  { label: '待发货', value: 2 },
  { label: '已发货', value: 3 },
  // { label: '待收货', value: 4 },
  // { label: '已签收', value: 5 },
  { label: '待收货', value: 4 },
  { label: '待收货', value: 5 },
  { label: '待收货', value: 45 },
  { label: '售后中', value: 6 },
  { label: '售后完成', value: 7 },
  { label: '已完成', value: 8 },
  { label: '已取消', value: 9 }
]

export const getStatusLabel = (order: object) => {
  if(order.status === 1) {
    return order.payed ? '已付款' : '待付款'
  }
  return ORDER_STATUS_LIST.find((item) => item.value === order.status)?.label || '--'
}

export const ORDER_AUDIT_STATUS_LIST = [
  { label: '未审批', value: 1 },
  { label: '审批中', value: 2 },
  { label: '审批驳回', value: 3 },
  { label: '审批通过', value: 4 }
]

export const ORDER_PAYMENT_METHOD_LIST = [
  { label: '账期支付', value: 1 },
  { label: 'VOP余额支付', value: 2 },
  { label: '线下支付', value: 3 },
  { label: '货到付款', value: 4 },
  { label: '线上支付', value: 5 },
  { label: '积分支付', value: 6 },
  { label: '混合支付', value: 7 }
]

export const AFTER_SALE_WAY_LIST = [
  { label: '仅退款', value: 10 },
  { label: '退货退款', value: 20 }
]

export const AFTER_SALE_REASON_LIST = [
  { name: '与商家协商一致退款', value: '100', way: [10, 20] },
  { name: '拍错/多拍/不喜欢', value: '110', way: [10, 20] },
  { name: '未按约定时间发货', value: '120', way: [10, 20] },
  { name: '快递/物流一直未送到', value: '130', way: [10] },
  { name: '货物破损已拒签', value: '140', way: [10] },
  { name: '少件/漏发', value: '150', way: [10, 20] },
  { name: '材质/面料与商品描述不符', value: '160', way: [20] },
  { name: '颜色/图案/款式等不符', value: '170', way: [20] },
  { name: '大小/尺寸/重量/厚度不符', value: '180', way: [20] },
  { name: '质量问题', value: '190', way: [20] },
  { name: '包装/商品破损/污渍', value: '200', way: [20] },
  { name: '假冒品牌', value: '210', way: [20] },
  { name: '卖家发错货', value: '220', way: [20] },
  { name: '发票问题', value: '230', way: [20] }
]

export const formatDateTime2 = (date, format = 'yyyy/MM/dd HH:mm:ss') => {
  const o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 小时
    'H+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
    a: date.getHours() < 12 ? '上午' : '下午', // 上午/下午
    A: date.getHours() < 12 ? 'AM' : 'PM', // AM/PM
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (`${ date.getFullYear()  }`).substr(4 - RegExp.$1.length));
  }
  for (const k in o) {
    if (new RegExp(`(${  k  })`).test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : (`00${  o[k] }`).substr((`${  o[k] }`).length)
      );
    }
  }

  return format;
}

export const formatDate = (mills, format = 'yyyy/MM/dd') => {
  if(!mills) {
    return ''
  }
  const date = new Date(mills)
  return formatDateTime2(date, format)
}

export const formatDateTime = (mills, format = 'yyyy/MM/dd HH:mm:ss') => {
  if(!mills) {
    return ''
  }
  const date = new Date(mills)
  return formatDateTime2(date, format)
}

export const formatMoney = (val, digits = 2) => {
  if(!val) {
    val = 0
  }
  const t1 = Math.pow(10, digits)
  return ((val * t1)/t1).toFixed(digits)
}
export const getOrderLabel = (status) => {
  const obj = ORDER_STATUS_LIST.find(item => item.value === status)
  return obj ? obj.label : '--'
}

export const getOrderAuditLabel = (status) => {
  const obj = ORDER_AUDIT_STATUS_LIST.find(item => item.value === status)
  return obj ? obj.label : '--'
}

export const getPaymentMethodLabel = (status = 1) => {
  const obj = ORDER_PAYMENT_METHOD_LIST.find(item => item.value === status)
  return obj ? obj.label : '--'
}

export const getAfterSaleWayLabel = (status) => {
  const obj = AFTER_SALE_WAY_LIST.find(item => item.value === status)
  return obj ? obj.label : '--'
}

export const getAfterSaleReasonLabel = (status) => {
  const obj = AFTER_SALE_REASON_LIST.find(item => item.value === status)
  return obj ? obj.name : '--'
}
