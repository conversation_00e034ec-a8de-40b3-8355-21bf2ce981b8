<template>
  <div class="layout">
    <div class="page-container">
      <router-view v-slot="{ Component }">
        <keep-alive include="Home,HomeSearch,ClassifyFilter,CollectionZone" :key="comKey" :max="10">
          <component :is="Component" />
        </keep-alive>
      </router-view>
    </div>
  </div>
  <van-tabbar
    v-if="showTabbar"
    :fixed="true"
    v-model="active"
    @change="onChange"
    class="tabbar"
  >
    <van-tabbar-item name="home">
      <span>首页</span>
      <template #icon="props">
        <img :src="props.active ? homeActive : home" class="tabbar-icon" />
      </template>
    </van-tabbar-item>
    <van-tabbar-item name="classify" icon="apps-o">
      <span>分类</span>
      <template #icon="props">
        <img :src="props.active ? classifyActive : classify" class="tabbar-icon" />
      </template>
    </van-tabbar-item>
    <van-tabbar-item name="shoppingCart" icon="orders-o" :badge="cartCount" :badge-props="{'show-zero': false}">
      <span>购物车</span>
      <template #icon="props">
        <img :src="props.active ? shoppingCartActive : shoppingCart" class="tabbar-icon" />
      </template>
    </van-tabbar-item>
    <van-tabbar-item name="mine" icon="friends-o">
      <span>我的</span>
      <template #icon="props">
        <img :src="props.active ? mineActive : mine" class="tabbar-icon" />
      </template>
    </van-tabbar-item>
  </van-tabbar>
</template>

<script setup lang="ts">
import { ref, computed, watchEffect } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import home from '@/assets/tabbar/home.png'
import homeActive from '@/assets/tabbar/home-select.png'
import classify from '@/assets/tabbar/classify.png'
import classifyActive from '@/assets/tabbar/classify-select.png'
import shoppingCart from '@/assets/tabbar/shoppingcart.png'
import shoppingCartActive from '@/assets/tabbar/shoppingcart-select.png'
import mine from '@/assets/tabbar/mine.png'
import mineActive from '@/assets/tabbar/mine-select.png'
import { useHomeStore } from '@/stores/home'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const active = ref('home')
const router = useRouter()
const route = useRoute()

const homeStore = useHomeStore()
const comKey = computed(() => homeStore.cacheKey )
const cartCount = computed(() => userStore.shoppingCart.count || 0 )

const showTabbar = computed(() => {
  return ['Home', 'Classify', 'ShoppingCart', 'Mine'].includes(route.name)
})


const onChange = (index: string) => {
  router.push({
    path: `/${index}`
  })
}

watchEffect(() => {
  let routePath = <string>route.path
  active.value = routePath.substring(1, routePath.length)
})
</script>

<style scoped lang="scss">
.layout {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 100%;
  flex: 1 1 100%;
  position: relative;

  .page-container {
    z-index: 2;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-flow: column;
    background-color: #F2F2F2;
    overflow-y: auto;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}

.tabbar {
  height: var(--van-tabbar-height);
  flex-shrink: 0;
  z-index: 100;
  :deep(.van-tabbar-item__text) {
    font-size: 10px;
  }
  .tabbar-icon {
    width: 26px;
    height: 26px;
  }
}

</style>
