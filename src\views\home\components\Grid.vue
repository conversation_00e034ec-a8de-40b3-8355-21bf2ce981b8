<template>
  <div class="session__grid mt-8">
    <div class="session__grid-item flex flex-col items-center" v-for="(item, idx) in imgList" :key="idx" @click="jumpToClassify(item)">
      <div class="image-box w-64 h-64">
        <van-image :src="item.iconH5" class="w-64 h-64 image" fit="cover" style="max-width:99px;max-height:99px;" />
      </div>
      <span class="font500 text mt-4">{{ item.categoryName }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'Grid'
})

import { ref } from 'vue'
import { useRouter } from 'vue-router';

import type { categoryList } from '@/api/home'
import { getRootCategoryList } from '@/api/home'

const imgList = ref<categoryList[]>()

const getImgList = async () => {
  const res = await getRootCategoryList()
  imgList.value = res.slice(0, 10) || []
}

getImgList()

const router = useRouter()
const jumpToClassify = (category: any) => {
  router.push({
    path: '/classify/filter',
    query: {
      categoryName: category.categoryName,
      categoryId1: category.categoryId
    }
  })
}

</script>

<style scoped lang="scss">
.session__grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 8px 0;
  padding-bottom: 12px;

  .session__grid-item {
    .image-box {
      border-radius: 50%;
      background: #f2f2f2;
    }
  }

  .text {
    font-size: 12px;
    color: #393939;
    line-height: 12px;
  }

  .image {
    border-radius: 8px;
    overflow: hidden;
  }
}
</style>
