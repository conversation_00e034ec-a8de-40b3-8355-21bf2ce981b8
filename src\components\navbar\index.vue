<template>
  <van-nav-bar v-bind="$attrs" @click-left="clickLeftFunc">
    <template #left>
      <img :src="back" class="size-20" />
    </template>
    <template #right v-if="$slots.right">
      <slot name="right"></slot>
    </template>
    <template #title v-if="$slots.title">
      <slot name="title"></slot>
    </template>
  </van-nav-bar>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

import back from '@/assets/back.png'

const router = useRouter()

defineOptions({
  name: 'NavBar'
})

const props = defineProps({
  clickLeft: {
    type: Function
  }
})

const clickLeftFunc = () => {
  if(props.clickLeft) {
    props.clickLeft()
  } else {
    router.go(-1)
  }
}

</script>

<style scoped lang="scss">
.van-nav-bar {
  background: #f2f2f2;
}

:deep(.van-nav-bar__title) {
  color: #000;
  font-size: 17px;
  font-weight: 600;
}
</style>
