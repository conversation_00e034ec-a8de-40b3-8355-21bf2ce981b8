{"extends": "@tsconfig/node18/tsconfig.json", "include": ["vite.config.*", "vitest.config.*", "cypress.config.*", "nightwatch.conf.*", "playwright.config.*", "**/*.ts", "src/**/*.d.ts", "src/types/**/*.d.ts", "src/**/*.ts", "src/**/*.tsx", "src/api/*.ts", "src/**/*.vue"], "compilerOptions": {"composite": true, "noEmit": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "types": ["node"], "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/api/*": ["src/api/*"]}}}