import qs from 'qs'

import request from '@/utils/request'

export interface categoryList {
  categoryId: string,
  categoryName?: string,
  childCategoryList?: categoryList[],
  icon?: string,
  iconH5?: string,
}

export interface configInfoType {
  approveSwitch: boolean,
  projectSwitch: boolean,
  inited: false,
  domain: string,
  faviconUrl: string,
  loginType: number,
  logoutType: number,
  name: string,
  orderSwitch: boolean,
  tenantId: string,
  themeCode: number,
  title: string,
  payMethod: string,
  loginUrl: string,
  logoUrl: string,
  productField: string,
  globalTipSwitch: boolean,
  globalTipContent: string
}

export interface supplierItem {
  code: string,
  fullName: string,
  id: string,
  logoUrl: string,
  name: string,
  type: string,
  freightThreshold: number,
  freight: number,
  saleAmountMin: number
}

export interface homeConfigType {
  h5CategorySwitch?: boolean,
  h5SupplierSwitch?: boolean,
  seoCardSwitch?: boolean,
  seoCardList?: []
}

export interface styleConfigType {
  logoUrl: string,
  logoIconUrl: string,
  faviconUrl: string,
  diyCsss: string
}

export interface seoCardType {
  id: number,
  name: string,
  title: string,
  icon?: string,
  imageUrl?: string,
  type: number,
  layout?: number,
  memo?: string,
  sort?: number,
  content: string
}

const requestP = (data: any) => request({
  ...data,
  ...{
    baseURL: import.meta.env.VITE_BASE_PRODUCT_API
  }
})

// 查询内容列表
export function queryContentList(params: any) {
  return request({
    url: '/system/cms/content/list',
    method: 'get',
    params,
    paramsSerializer: params => qs.stringify(params, { arrayFormat: "repeat" })
  })
}

// 搜索商品
export function goodsSearchSimpleList(params: any) {
  return requestP({
    url: '/sku/goodsSearchSimpleList',
    method: 'get',
    params
  })
}

// 查询顶级大类
export function getRootCategoryList(): Promise<categoryList[]> {
  return request({
    url: '/product/category/getRootList',
    method: 'get'
  })
}

// 获取轮播图
export function getAdvPositionList(params: {
  type: number,
  terminalType: number,
  bizType: number
}) {
  return request({
    url: '/mall/adv-position/list',
    method: 'get',
    params
  })
}

// 获取全局配置信息
export function getConfig(params: {
  host: string
}): Promise<configInfoType> {
  return request({
    url: '/mall/basis-config/get',
    method: 'get',
    params
  })
}

// 获取首页配置信息
export function getHomeConfig(params: any): Promise<string> {
  return request({
    url: '/mall/home-config/v2/get',
    method: 'get',
    params
  })
}

// 获取供应商
export function getTopList(): Promise<supplierItem[]> {
  return request({
    url: '/mall/supplier/get-top-list',
    method: 'get'
  })
}

// 获取运营区域列表
export function getSeoCardList(params:any): Promise<seoCardType[]> {
  return request({
    url: '/product/seo-card/list',
    method: 'get',
    params
  })
}