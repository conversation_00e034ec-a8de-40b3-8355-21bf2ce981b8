<template>
  <div class="mb-10">
    <van-sticky>
      <NavBar title="物流信息" class="!bg-white" />
    </van-sticky>

    <template v-if="isJd">
      <van-tabs v-model:active="activeTab" v-if="jdDeliveryInfo.logisticInfoList.length > 1">
        <van-tab v-for="(item, index) in jdDeliveryInfo.logisticInfoList" :key="index" :title="'包裹' + (index + 1)"></van-tab>
      </van-tabs>

      <div v-if="curDelivery">
        <van-cell-group>
          <van-cell title="物流公司" :value="curDelivery.name"/>
          <van-cell title="物流单号" :value="curDelivery.num" @click="handleCopy(curDelivery.num)"/>
        </van-cell-group>
        <div class="p-10 rounded-10 lh-14 text-12 bg-white mt-10" v-for="(child, index) in curDelivery.trackInfoList" :key="index">
          <div class="text-[#999999]">{{ child.trackMsgTime }}  {{child.trackOperator}}</div>
          <div class="mt-10" :class="{ 'font-600': index === 0, 'text-[#999999]': index !== 0 }">{{ child.trackContent }}</div>
        </div>
      </div>
    </template>

    <template v-if="!isJd">
      <van-tabs v-model:active="activeTab" v-if="deliveryInfo.length > 1">
        <van-tab v-for="(item, index) in deliveryInfo" :key="index" :title="'包裹' + (index + 1)"></van-tab>
      </van-tabs>

      <div v-if="curDelivery">
        <van-cell-group>
          <van-cell title="物流公司" :value="curDelivery.name"/>
          <van-cell title="物流单号" :value="curDelivery.num" @click="handleCopy(curDelivery.num)"/>
        </van-cell-group>
        <van-card
          v-for="(sku,index) in curDelivery.skuList" :key="index"
          :title="sku.skuName"
          :desc="sku.skuId"
          :thumb="sku.picUrl"
        />
        <div class="p-10 rounded-10 lh-14 text-12 bg-white mt-10" v-for="(child, index) in curDelivery.trackList" :key="index">
          <div class="text-[#999999]">{{ child.deliveryTime }} </div>
          <div class="mt-10" :class="{ 'font-600': index === 0, 'text-[#999999]': index !== 0 }">{{ child.content }}</div>
        </div>
      </div>
    </template>

  </div>
</template>

<script setup lang="ts">
import { computed,onMounted, ref } from 'vue'

defineOptions({
  name: 'Logistics'
})

import { useClipboard } from '@vueuse/core/index'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

import { orderDelivery } from '@/api/order'
import NavBar from '@/components/navbar/index.vue'

const { copy } = useClipboard()
const handleCopy = async (num: string) => {
  await copy(num)
  showToast('复制成功')
}

const deliveryInfo = ref([])
const jdDeliveryInfo = ref({})
const isJd = ref(false)
const activeTab = ref(0)

const curDelivery = computed(() => {
  if(deliveryInfo.value) {
    if(isJd.value) {
      const logisticsInfo = jdDeliveryInfo.value.logisticInfoList[activeTab.value]
      return {
        name: logisticsInfo.deliveryCarrier,
        num: logisticsInfo.deliveryOrderId,
        trackInfoList: jdDeliveryInfo.value.trackInfoList
      }
    }
    return deliveryInfo.value[activeTab.value]

  }
  return null
})

const route = useRoute()
const getList = () => {
  orderDelivery({
    orderNo: route.query.orderNo as string
  }).then((res) => {
    if (res?.supplierType === 1) {
      jdDeliveryInfo.value = res.jdDeliveryInfo || {}
      isJd.value = true
    } else {
      deliveryInfo.value = res.deliveryInfo || []
      isJd.value = false
    }
  })
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss"></style>
