import type { LocationQueryValue } from 'vue-router'

import request from '@/utils/request'

const requestO = (data: any) =>
  request({
    ...data,
    ...{
      baseURL: import.meta.env.VITE_BASE_TRADE_API
    }})

const requestP = (data: any) =>
  request({
    ...data,
    ...{
      baseURL: import.meta.env.VITE_BASE_PAY_API
    }})

export interface Request {
  /**
   * 页码，从 1 开始
   */
  pageNo: number
  /**
   * 每页条数，最大值为 100
   */
  pageSize: number
  /**
   * 订单状态
   */
  status?: number
  userId?: number
  [property: string]: any
}

/**
 * Item，用户 App - 交易订单的明细的订单项目
 */
export interface Item {
  canComment?: boolean
  /**
   * 是否评论
   */
  commented: boolean
  /**
   * 购买数量
   */
  count: number
  /**
   * 编号
   */
  id: number
  /**
   * 商品图片
   */
  picUrl: string
  /**
   * 商品 SKU 编号
   */
  skuId: number
  /**
   * 商品名称
   */
  skuName: string
  /**
   * 商品单价
   */
  skuPrice: number
  /**
   * 商品总价
   */
  skuTotalPrice: number
  [property: string]: any
}

/**
 * AppTradeOrderPageItemRespVO，用户 App - 订单交易的分页项 Response VO
 */
export interface AppTradeOrderPageItemRespVO {
  /**
   * 审核结果
   */
  auditResult: string
  /**
   * 审核状态
   */
  auditStatus: number
  /**
   * 账单状态
   */
  billStatus?: number
  /**
   * 订单创建时间
   */
  createTime: Date
  /**
   * 订单编号
   */
  id: number
  /**
   * 发票状态
   */
  invoiceStatus?: number
  items?: Item[]
  /**
   * 订单流水号
   */
  no: string
  /**
   * 订单金额
   */
  orderPrice: number
  /**
   * 父订单流水号
   */
  parentNo: string
  /**
   * 购买的商品数量
   */
  productCount: number
  /**
   * 收件人名称
   */
  receiverName: string
  /**
   * 是否选中
   */
  selected: boolean
  /**
   * 订单状态
   */
  status: number
  /**
   * 供应商ID
   */
  supplierId?: number
  /**
   * 供应商名称
   */
  supplierName?: string
  [property: string]: any
}

/**
 * PageResultAppTradeOrderPageItemRespVO，分页结果
 */
export interface PageResultAppTradeOrderPageItemRespVO {
  /**
   * 数据
   */
  list: AppTradeOrderPageItemRespVO[]
  /**
   * 当前页
   */
  pageNum: number
  /**
   * 总页数
   */
  pages: number
  /**
   * 每页的数量
   */
  pageSize: number
  /**
   * 当前页数量
   */
  size: number
  /**
   * 总量
   */
  total: number
  [property: string]: any
}

/**
 * CommonResultPageResultAppTradeOrderPageItemRespVO
 */
export interface Response {
  code?: number
  data?: PageResultAppTradeOrderPageItemRespVO
  msg?: string
  [property: string]: any
}

// 创建订单
export function orderCreate(data) {
  return requestO({
    url: '/order/create',
    method: 'post',
    data
  })
}

// 获得订单交易分页
export function orderPage(params: {
  pageNo: any
  pageSize: any
  status: String | Number | undefined
}): Promise<PageResultAppTradeOrderPageItemRespVO> {
  return requestO({
    url: '/order/page',
    method: 'get',
    params
  })
}

// 获得交易订单
export function orderDetail(params: { orderNo: string | null | LocationQueryValue[] }): Promise<any> {
  return requestO({
    url: '/order/get-detail',
    method: 'get',
    params
  })
}

// 取消订单
export function orderCancel(data: { orderNo: string; cancelReason?: string }): Promise<any> {
  return requestO({
    url: '/order/cancel-order',
    method: 'post',
    data
  })
}

// 确认收货
export function orderReceive(data: { orderNo: string }): Promise<any> {
  return requestO({
    url: `/order/receiveOrder?orderNo=${ data.orderNo }`,
    method: 'post',
    data
  })
}

// 删除订单
export function orderDelete(data: { orderNo: string }) {
  return requestO({
    url: `/order/delete-order/${ data.orderNo }`,
    method: 'post'
  })
}

// 根据积分数量
export function getOrderNum(): Promise<any> {
  return requestO({
    url: '/order/get-order-stats',
    method: 'get'
  })
}

// 获得交易订单
export function getDetailOrder(params: { orderNo: string }): Promise<any> {
  return requestO({
    url: '/order/get-detail',
    method: 'get',
    params
  })
}

// 支付订单
export function getPayOrder(data: { orderNo: string }) {
  return requestO({
    url: `/order/payOrder`,
    method: 'post',
    data
  })
}

// 检查订单支付状态
export function checkOrderPayed(data: any) {
  return requestO({
    url: `/order/checkPayed`,
    method: 'post',
    data
  })
}

// 查询支付渠道列表
export function getChannelList(params: any) {
  return requestP({
    url: `/channel/get-enable-code-list`,
    method: 'get',
    params: params
  })
}

// 提交支付单
export function submitPayOrder(data: any) {
  return requestP({
    url: `/order/submit`,
    method: 'post',
    data: data
  })
}

// 查询售后服务组件URL
export function getAfterSaleComponentUrl(params: {orderId: number}) {
  return requestO({
    url: '/vopComponent/getAfterSaleComponentUrl',
    method: 'get',
    params
  })
}

// 根据订单号查询物流信息
export function orderDelivery(params: { orderNo: string }): Promise<any> {
  return requestO({
    // url: '/delivery/get-by-order',
    url: '/order/get-delivery',
    method: 'get',
    params
  })
}
