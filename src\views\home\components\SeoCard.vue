
<template>
  <div class="seo-card">
    <div class="p-12 mt-8 bg-white">
      <div class="brand__item" v-for="(card,index) in imageCardList" :key="index" @click="hanleImageClick(card)">
        <van-image :src="card.imageUrl" class="brand__item-img h-86 w-full" />
      </div>
    </div>

    <Product v-if="!loading" ref="productRef" :tagIds="getTagIds(firstSkuCard)" :title="firstSkuCard.title"/>
  </div>
</template>

<script setup lang="ts">
import { showToast } from 'vant'
import { computed,onMounted,ref } from 'vue'
import { useRouter } from "vue-router"

import type { seoCardType } from '@/api/home'
import { getSeoCardList } from '@/api/home'
import { useHomeStore } from '@/stores/home'
import Product from '@/views/home/<USER>/Product.vue'

defineOptions({
  name: 'SeoCard'
})

const router = useRouter()
const homeStore = useHomeStore()
const configData = homeStore.configInfo.configData
const homeConfig = homeStore.configInfo.homeConfig
const cardList = ref<seoCardType[]>([])
const productRef = ref(null)
const loading = ref(true)

const cardIdList = computed(() => {
  if(homeConfig && homeConfig.seoCardList) {
    return (homeConfig.seoCardList || []).filter(item => item.title).map(item => item.id)
  }
  return []
})

const imageCardList = computed(() => cardList.value.filter(card => card.type === 10))
const skuCardList = computed(() => cardList.value.filter(card => card.type === 20))
const firstSkuCard = computed(() => skuCardList.value && skuCardList.value.length ? skuCardList.value[0] : {})

const readCache = () => {
  const key = `jcyh5-home-seocard-${ configData.tenantId }`
  const cacheVal = window.localStorage.getItem(key)
  if(cacheVal) {
    try {
      cardList.value = JSON.parse(cacheVal)
    } catch (e) { /* empty */ }
  }
}

const setCache = (obj:any) => {
  const key = `jcyh5-home-seocard-${ configData.tenantId }`
  window.localStorage.setItem(key, JSON.stringify(obj))
}

const doLoadCardList = async () => {
  const params = {
    ids: cardIdList.value.join(',')
  }
  const data = await getSeoCardList(params)
  cardList.value = data || []
  setCache(cardList.value)
  loading.value = false
}

const loadCardList = () => {
  readCache()
  doLoadCardList()
}

onMounted(() => {
  loadCardList()
})

const getCardContentObj = (card:any) => {
  if(card && card.content) {
    return JSON.parse(card.content)
  }
  return null
}

const getTagIds = (card:any) => {
  if(card.content) {
    const obj = getCardContentObj(card)
    console.log('seo-card: ', obj)
    if(obj && obj.tags) {
      return obj.tags.map(tag => tag.id).join(',')
    }
  }
  return null
}

const hanleImageClick = (card:any) => {
  const obj = getCardContentObj(card)
  const tagIds = getTagIds(card)
  console.log('seo-card: ', obj)
  if(obj && obj.linkUrl) {
    window.location.href = obj.linkUrl
  } else if(tagIds) {
    const query = {
      name: card.title,
      tagIds: getTagIds(card)
    }
    router.push({
      path: '/home/<USER>',
      query: query
    })
  } else {
    showToast('参数配置异常，请联系管理员')
  }
}

</script>

<style lang="scss">
.seo-card {
  .brand__item {
    font-size: 0;
    & + .brand__item {
      margin-top: 8px;
    }
  }
}
</style>