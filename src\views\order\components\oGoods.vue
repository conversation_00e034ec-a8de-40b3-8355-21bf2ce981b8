<template>
  <div @click="toDetail">
    <div class="mb-12 text-black font-600 lh-16">{{ goods.supplierName }}</div>
    <div class="flex items-center justify-between text-10 lh-17">
      <div class="text-[#999999]">订单号：{{ goods.no }}</div>
      <div class="text-[#999999]" v-if="goods.thirdOrderId">外部订单号：{{ goods.thirdOrderId }}</div>
      <div class="color-primary">{{ getStatusLabel(goods) }}</div>
    </div>
    <div class="flex flex-col mt-10" v-for="(item, idx) in goods.items" :key="idx">
      <div class="flex">
        <img :src="item.picUrl || item.imgUrl" class="size-94 rounded-5 flex-shrink-0" @click.stop="toGoodsDetail(item)"/>
        <div class="ml-10 flex flex-col flex-1">
          <div class="flex items-center justify-between">
            <div class="font-600 text-14 text-black ellipsis-2 flex-1" @click.stop="toGoodsDetail(item)">
              {{ item.skuName }}
            </div>
            <div class="text-[#6D6C6C] text-12 lh-14 w-46 flex-shrink-0 text-right">
              x{{ item.count || item.skuNum }}
            </div>
          </div>

          <div class="mt-8">
            <!--          <span class="text-12 text-[#6c6c6c] bg-[#f2f2f2] rounded-10 px-8 py-4 lh-12">黑胡椒味400g</span>-->
          </div>
          <!--        <div class="ellipsis-1 text-[#999999] text-10 lh-12 mt-23">备注：给我打包好，快递不要放在菜鸟驿站在菜鸟...</div>-->
        </div>
      </div>
      <div v-if="afterSales" class="mt-12 text-right">
        <van-button
          v-if="configData.productCommentSwitch && !item.commented && item.canComment" size="mini"
          round plain class="!h-30 !px-10" color="#999999" @click.stop="doComment(goods.no, item.skuId)">我要评价</van-button
        >
        <van-button
          v-if="item.canAfterSale && [2, 3, 4, 5, 6, 7, 8].includes(goods.status)" size="mini"
          round plain class="!h-30 !px-10" color="#999999" @click.stop="doAfterSale(goods, item)">{{ getAfterSaleBtnText(item, goods) }}</van-button
        >
      </div>
    </div>
    <div class="flex items-center justify-end text-12 text-[#999999] mt-12">
      <div>合计￥{{ formatMoney(goods.orderPrice || goods.orderTotalPrice) }}</div>
      <div class="ml-10">积分{{ formatMoney(goods.payScore || 0) }}</div>
      <div class="text-[#000000] ml-10 font-600">现金￥{{ formatMoney(goods.payPrice || 0) }}</div>
    </div>
    <div v-if="showBottom" class="flex items-center justify-end mt-12">
      <van-button
        round
        plain
        size="mini"
        class="!mr-10 !h-30 !px-10"
        color="#999999"
        v-if="goods.status === 1"
        @click.stop="btnHandle('cancel')"
        >取消订单</van-button
      >
      <!--        <van-button round plain class="!mr-10 !h-30 !px-10" color="#999999">申请退货</van-button>-->
      <van-button
        round
        plain
        class="!mr-10 !h-30 !px-10"
        color="#999999"
        size="mini"
        v-if="[3, 4, 5, 8].includes(goods.status)"
        @click.stop="btnHandle('logistics')"
        >查看物流</van-button
      >
      <van-button
        round
        plain
        class="!mr-10 !h-30 !px-10"
        color="#999999"
        size="mini"
        v-if="goods.status === 9"
        @click.stop="btnHandle('delete')"
        >删除订单</van-button
      >
      <van-button
        round
        plain
        class="!h-30 !px-10 btn-primary"
        size="mini"
        v-if="[3, 4].includes(goods.status)"
        @click.stop="btnHandle('receive')"
        >确认收货</van-button
      >
      <van-button
        round
        plain
        class="!h-30 !px-10 btn-primary"
        size="mini"
        v-if="goods.status === 8"
        @click.stop="btnHandle('again')"
        >再来一单</van-button
      >
      <van-button round plain size="mini" class="!h-30 !px-10 btn-primary" v-if="goods.status === 1 && !goods.payed" @click.stop="handlePayOrder">立即付款</van-button>
    </div>
  </div>
  <cancel ref="cancelRef" @submit="submitCancel"></cancel>
</template>

<script setup lang="ts">
import { showConfirmDialog } from 'vant'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

import { getAfterSaleComponentUrl, orderCancel, orderDelete, orderReceive } from '@/api/order'
import { useHomeStore } from '@/stores/home'
import { formatMoney } from '@/utils/base'
import { getStatusLabel } from '@/utils/orderUtils'

import cancel from './cancel.vue'
import { eventNames } from 'process'

defineOptions({
  name: 'OGoods'
})

const props = defineProps({
  showBottom: {
    type: Boolean,
    default: false
  },
  afterSales: {
    type: Boolean,
    default: false
  },
  goods: {
    type: Object,
    default: () => {}
  },
  orderNo: {
    type: String,
    default: ''
  }
})

const homeStore = useHomeStore()
const router = useRouter()
const configData = homeStore.configInfo.configData || {}

const toDetail = () => {
  router.push(`/order/orderDetail?no=${ props.goods.no }`)
}

const getAfterSaleBtnText = (orderItem: any, order: any) => {
  const status = orderItem.afterSaleStatus
  if(status === 1) {
    return '退款中'
  } else if(status === 2) {
    return '退款完成'
  } else if(orderItem.afterSaleMemo ) {
    return orderItem.afterSaleMemo
  } else if(order && order.status !==9 ) {
    return '售后申请'
  }
  return ''
}

const doAfterSale = async (order, orderItem) => {
  if(order.supplierType === 1) {
    const data = await getAfterSaleComponentUrl({
      orderItemId: orderItem.id ? orderItem.id : orderItem.orderItemId
    })
    if (data) {
      window.location.href = data
    }
  } else if(order.status !== 9) {
    if(orderItem.afterSaleMemo || [1,2].includes(orderItem.afterSaleStatus)) {
      toInspect(orderItem)
    } else {
      toApply(orderItem)
    }
  }
}

const doComment = async (no: any, skuId: any) => {
  router.push({
    path: '/comment/create',
    query: {
      no: no,
      skuId: skuId
    }
  })
}

const handlePayOrder = () => {
  router.push({
    path: '/order/pay',
    query: {
      no: props.goods.no
    }
  })
}

const toInspect = (item) => {
  router.push({
    path: '/sales/inspect',
    query: {
      orderItemId: item.id || item.orderItemId,
      orderNo: props.goods.no
    }
  })
}

const toApply = (item) => {
  router.push({
    path: '/sales/apply',
    query: {
      orderItemId: item.id || item.orderItemId,
      orderNo: props.goods.no
    }
  })
}

const cancelRef = ref()
const emits = defineEmits(['refresh'])
const btnHandle = async (type: string) => {
  if (type === 'receive') {
    await orderReceive({ orderNo: props.goods.no })
    emits('refresh')
  } else if (type === 'cancel') {
    console.log(cancelRef, cancelRef.value, cancelRef.value.show)
    cancelRef.value.show = true
  } else if (type === 'delete') {
    showConfirmDialog({
      title: '提示',
      message: '确定删除订单吗？'
    })
      .then(async () => {
        await orderDelete({ orderNo: props.goods.no })
        emits('refresh')
      })
      .catch(() => {
        // on cancel
      })
  } else if (type === 'again') {
    router.push({
      path: '/goods/detail',
      query: {
        skuId: props.goods.items[0].skuId,
        supplierId: props.goods.supplierId
      }
    })
  } else if (type === 'logistics') {
    router.push({
      path: '/order/logistics',
      query: {
        orderNo: props.goods.no
      }
    })
  }
}

const toGoodsDetail = (data: any) => {
  const supplierId = data.supplierId
  const skuId = data.skuId
  if(!supplierId || !skuId) {
    toDetail()
    return
  }
  router.push({
    path: '/goods/detail',
    query: {
      supplierId: supplierId,
      skuId: skuId
    }
  })
}

const submitCancel = async (cancelReason: string) => {
  await orderCancel({ orderNo: props.goods.no, cancelReason })
  emits('refresh')
}
</script>

<style scoped lang="scss"></style>
