<template>
  <div class="page-content">
    <!-- <van-pull-refresh v-model="refreshLoading" :head-height="80" @refresh="onRefresh"> -->
      <van-sticky :offset-top="0">
        <div class="headerLogo flex-center pt-5 pb-8 bg-white">
          <img :src="wdlogo" class="w-251" />
        </div>
        <div class="search">
          <van-search
            v-model="searchValue"
            shape="round"
            :left-icon="search"
            placeholder="搜索商品名称"
            readonly
            @click="$router.push('/home/<USER>')"
          />
        </div>
      </van-sticky>
      <div class="home-content h-100vh overflow-y-auto" ref="homeScrollContainer">
        <div class="px-10 bg-white">
          <Swipe />

          <div class="mt-10">
            <van-notice-bar left-icon="volume-o" :scrollable="false" @click="$router.push({ path: '/notice/index' })">
              <template #right-icon>
                更多<van-icon name="arrow"></van-icon>
              </template>
              <van-swipe
                vertical
                class="notice-swipe"
                :autoplay="3000"
                :touchable="false"
                :show-indicators="false"
              >
                <van-swipe-item v-for="(content,index) in latestContent" :key="index" @click.stop="showContentDetail(content)">{{ content.title }}</van-swipe-item>
              </van-swipe>
            </van-notice-bar>
          </div>

          <Grid v-if="homeConfig.h5CategorySwitch" />
        </div>
        <Supplier v-if="homeConfig.h5SupplierSwitch" />
        <SeoCard v-if="homeConfig.seoCardSwitch" />
        <template v-else>
          <Zone />
          <Product ref="productRef"/>
        </template>

      </div>
    <!-- </van-pull-refresh> -->
    <EntryTips />
  </div>
</template>

<script setup lang="ts">
import { nextTick, onActivated, onMounted, onUnmounted,ref } from 'vue'
import { useRouter } from 'vue-router'

import { contentList } from '@/api/notice'
import search from '@/assets/home/<USER>'
import wdlogo from '@/assets/home/<USER>'
import { useHomeStore } from '@/stores/home'
import EntryTips from '@/views/home/<USER>/EntryTips.vue'
import Grid from '@/views/home/<USER>/Grid.vue'
import Product from '@/views/home/<USER>/Product.vue'
import SeoCard from '@/views/home/<USER>/SeoCard.vue'
import Supplier from '@/views/home/<USER>/Supplier.vue'
import Swipe from '@/views/home/<USER>/Swipe.vue'
import Zone from '@/views/home/<USER>/Zone.vue'

defineOptions({
  name: 'Home'
})

const router = useRouter()
const searchValue = ref('')
const refreshLoading = ref(false)
const productRef = ref(null)

const homeStore = useHomeStore()
const homeConfig = homeStore.configInfo.homeConfig

const latestContent = ref({})
const getLatestContent = () => {
  contentList({
    page: 1,
    pageSize: 5,
    categoryType: 20
  }).then(data => {
    latestContent.value = data?.list || []
    console.log('latestContent', latestContent)
  })
}

const showContentDetail = (content:Object) => {
  if(!content.id) {
    return
  }
  router.push({
    name: 'NoticeDetail',
    query: {
      id: content.id
    }
  })
}

const onRefresh = () => {
  refreshLoading.value = true
  setTimeout(() => {
    getLatestContent()
    productRef.value.refresh()
    refreshLoading.value = false
  }, 500)
}

getLatestContent()

const homeScrollContainer = ref(null)
const scrollPosition = ref(0)
onMounted(() => {
  if (homeScrollContainer.value && homeScrollContainer.value.addEventListener) {
    homeScrollContainer.value.addEventListener('scroll', handleScroll)
  }
})

onUnmounted(() => {
  if (homeScrollContainer.value && homeScrollContainer.value.removeEventListener) {
    homeScrollContainer.value.removeEventListener('scroll', handleScroll)
  }
})

onActivated(() => {
  nextTick(() => {
    if(homeScrollContainer.value) {
      homeScrollContainer.value.scrollTop = scrollPosition.value
    }
  })
})

function handleScroll() {
  scrollPosition.value = homeScrollContainer.value.scrollTop
}

</script>

<style scoped lang="scss">
.page-content {
  .headerLogo {
    border-bottom: 1px solid #e6e6e6;
  }

  .search {
    :deep(.van-search) {
      padding: 8px 15px;
      .van-search__field {
        height: 36px;
      }
      .van-field__control {
        &::placeholder {
          color: #999;
        }
      }
    }
  }

  .desc {
    background: #fffeed;

    .more {
      color: #999999;
    }
  }
}
.notice-swipe {
  height: 40px;
  line-height: 40px;
}

</style>
