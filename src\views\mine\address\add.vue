<template>
  <div>
    <NavBar title="新增收货地址"></NavBar>
    <div class="p-20 rounded-10 bg-white m-10">
      <van-form ref="formRef" label-width="62px">
        <van-cell-group inset>
          <template v-if="!form.id && inited">
            <van-field
              v-model="addressText"
              rows="3"
              autosize
              label=""
              type="textarea"
              clearable
              maxlength="120"
              placeholder="请粘贴信息，自动拆分姓名，电话，地址"
            >
            </van-field>
            <div>
              <van-button round size="small" type="default" @click="clearAddressText">清空内容</van-button>
              <van-button round size="small" type="primary" @click="recognizeAddressText" style="margin-left: 10px;">智能识别</van-button>
            </div>
          </template>
          <van-field
            v-model.trim="form.name"
            name="name"
            :border="false"
            label="收货人"
            placeholder="请填写收货人"
            :rules="[{ required: true, message: '请填写收货人' }]"
          />
          <van-field
            v-model.trim="form.mobile"
            name="mobile"
            :border="false"
            maxlength="11"
            label="手机号"
            placeholder="请填写收货人手机号"
            :rules="[
              { required: true, message: '请填写收货人手机号' },
              { pattern: /^1[3-9]\d{9}$/, message: '手机号格式错误' }
            ]"
          />
          <van-field
            v-model.trim="form.location"
            name="location"
            :border="false"
            readonly
            label="所在地区"
            placeholder="省、市、区、街道"
            :right-icon="location"
            :rules="[{ required: true, message: '省、市、区、街道' }]"
            @click="showAreaHandle"
          />
          <van-field
            v-model.trim="form.consigneeAddress"
            name="consigneeAddress"
            type="textarea"
            autosize
            rows="2"
            :border="false"
            label="详细地址"
            placeholder="填写详情地址，如街道楼牌号等"
            :rules="[{ required: true, message: '填写详情地址，如街道楼牌号等' }]"
          />
          <div class="flex items-center justify-between">
            <div class="flex flex-col">
              <div class="text-black font-600">设置默认地址</div>
              <div class="text-[#999999] mt-5">提醒：每次下单会默认使用该地址</div>
            </div>
            <van-switch v-model="form.defaulted" />
          </div>
        </van-cell-group>
      </van-form>
    </div>
    <div class="mt-40 px-15">
      <van-button round block class="btn-primary" @click="onSubmit" :loading="saveLoading" :disabled="!canSave"> 保存 </van-button>
      <van-button class="!mt-16" round block @click="deleteAddress" v-if="form.id"> 删除 </van-button>
    </div>

    <AddressComp ref="AddressCompRef" @selectedAddress="selectedAddress" />
  </div>
</template>

<script setup lang="ts">
import addressParse from 'address-parse'
import { showConfirmDialog } from 'vant'
import { ref, watchEffect } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import type { AppAddressRespVO } from '@/api/address'
import { addressCreate, addressDelete, addressGet, addressUpdate, parseAddress } from '@/api/address'
import location from '@/assets/order/location.png'
import AddressComp from '@/components/address/index.vue'
import NavBar from '@/components/navbar/index.vue'
import { useUserStore } from '@/stores/user'

defineOptions({
  name: 'AddAddress'
})

const AddressCompRef = ref()
const userStore = useUserStore()
const inited = ref(false)

const form = ref<AppAddressRespVO>({
  id: null,
  name: '',
  mobile: '',
  location: '',
  consigneeAddress: '',
  provinceId: '',
  cityId: '',
  countyId: '',
  townId: '',
  provinceName: '',
  cityName: '',
  countyName: '',
  townName: '',
  defaulted: false
})

const formRef = ref()
const addressText = ref('')
const route = useRoute()
const router = useRouter()
const saveLoading = ref(false)
const canSave = ref(true)

const clearAddressText = () => {
  addressText.value = ''
}

const recognizeAddressText = () => {
  if(addressText.value && addressText.value.length > 5) {
    const parsedResult = addressParse.parse(addressText.value)
    console.log('parsedResult====', parsedResult)
    const result1 = parsedResult[0]

    form.value.name = result1.name
    form.value.mobile = result1.mobile
    form.value.consigneeAddress = result1.details

    if(!result1.province || !result1.city) {
      return
    }
    const detailAddress = result1.province + result1.city + result1.area + result1.details
    parseAddress({content: detailAddress}).then(data => {
      console.log('data===', data)
      form.value.provinceId = data.provinceId
      form.value.provinceName = data.provinceName
      form.value.cityId = data.cityId
      form.value.cityName = data.cityName
      form.value.countyId = data.countyId
      form.value.countyName = data.countyName
      form.value.townId = data.townId
      form.value.townName = data.townName
      form.value.location = [data.provinceName, data.cityName, data.countyName, data.townName].join(' ')
    })
  }
}

const showAreaHandle = () => {
  // showArea.value = true
  let addressArr = null
  if (form.value.provinceId) {
    addressArr = [form.value.provinceId, form.value.cityId, form.value.countyId, form.value.townId]
  }
  AddressCompRef.value.open(addressArr)
}

const deleteAddress = () => {
  showConfirmDialog({
    title: '提示',
    message: '确认删除这个收货地址吗？'
  })
    .then(() => {
      addressDelete(form.value.id).then(() => {
        router.go(-1)
      })
    })
    .catch(() => {
      // on cancel
    })
}

const onSubmit = () => {
  canSave.value = false
  saveLoading.value = true
  formRef.value
    .validate()
    .then(() => {
      let url = addressCreate
      if (route.query.id) {
        url = addressUpdate
      }
      url(form.value).then((res) => {
        userStore.loadUserAddress()
        router.go(-1)
      })
    })
    .catch((err: any) => {
      console.log(err)
    })
    .finally(() => {
      saveLoading.value = false
      canSave.value = true
    })
}
const showArea = ref(false)
const areaValue = ref('')
const selectedAreaValues = ref([])
const confirmHandle = ({ selectedValues, selectedOptions }) => {
  form.value.provinceId = selectedValues[0]
  form.value.cityId = selectedValues[1]
  form.value.countyId = selectedValues[2]
  form.value.townId = selectedValues[3] || ''

  form.value.provinceName = selectedOptions[0].text
  form.value.cityName = selectedOptions[1].text
  form.value.countyName = selectedOptions[2].text
  form.value.townName = selectedOptions[3] ? selectedOptions[3].text : ''

  selectedAreaValues.value = selectedValues
  form.value.location = selectedOptions.map((item) => item.text).join(' ')
  showArea.value = false
}

const selectedAddress = (val: any) => {
  form.value.provinceId = val[0].areaId
  form.value.cityId = val[1].areaId
  form.value.countyId = val[2].areaId
  form.value.townId = val[3]?.areaId || ''

  form.value.provinceName = val[0].areaName
  form.value.cityName = val[1].areaName
  form.value.countyName = val[2].areaName
  form.value.townName = val[3]?.areaName || ''

  form.value.location = val.map((item) => item.areaName).join(' ')
}

watchEffect(() => {
  const id = route.query.id as string
  if (!id) {
    inited.value = true
    return
  }
  addressGet({
    id: id
  }).then((res) => {
    form.value = res
    selectedAreaValues.value = [res.provinceId, res.cityId, res.countyId, res.townId]
    areaValue.value = String(res.countyId)
    form.value.location = [res.provinceName, res.cityName, res.countyName, res.townName].join(' ')
    inited.value = true
  })
})
</script>

<style scoped lang="scss">
:deep(.van-cell-group--inset) {
  margin: 0;
  .van-cell {
    padding-left: 0;
    padding-right: 0;
  }
  .van-field__label {
    margin-right: 10px;
    font-weight: 600;
    color: #000000;
    padding-top: 6px;
  }
  .van-field__body {
    background: #f2f2f2;
    border-radius: 4px;
    .van-field__right-icon {
      margin-right: 2px;
    }
  }
  .van-field__control {
    padding: 10px;
    height: 34px;
  }
}

:deep(.van-picker__confirm) {
  color: $primary-color;
}
</style>
