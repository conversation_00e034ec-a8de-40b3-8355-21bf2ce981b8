<template>
  <div class="loading-lock">
    <van-watermark :content="appName" />
    <van-loading v-if="loading" type="spinner" size="26px">
      自动登录中，请稍后...
    </van-loading>
    <div v-if="noPermission" style="font-size: 24px;color: #1d2129;text-align: center;margin-top: 48px;">
      抱歉，您无权限访问此页面
    </div>
  </div>
</template>

<script setup lang="ts">
import { showFailToast } from 'vant'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { ssoLogin } from '@/api/login'
import { setToken } from '@/utils/auth'

defineOptions({
  name: 'SsoLink'
})

const loading = ref(false)
const noPermission = ref(false)
const appName = ref('')

const router = useRouter()
const route = useRoute()
const loginPage = async (data: any) => {
  try {
    loading.value = true
    const result = await ssoLogin(data)
    setToken(result)
    sessionStorage.setItem('loginWay', 'sso')
    setTimeout(() => {
      loading.value = false
      const toPath = data.redirect || "/home";
      router.replace(toPath)
    }, 1000)
  } catch(e) {
    showFailToast('登录失败，自动跳至首页')
    router.replace('/home')
  }
}

const uid = route.query.uid
const tcode = route.query.tcode
const etoken = route.query.etoken || route.query.token
const redirect = route.query.redirect
const etype = route.query.etype
if (!tcode || !etoken) {
  noPermission.value = true
} else {
  loginPage({
    uid,
    tcode,
    etoken,
    redirect,
    etype
  })
}

appName.value = import.meta.env.VITE_BASE_APP_NAME || '金采通直采平台'

</script>

<style lang="scss">
.loading-lock {
  height: 500px !important;
  display: flex;
  align-items: center;
  justify-content: center;

  .van-loading__text {
    font-size: 16px !important;
  }
}

</style>

