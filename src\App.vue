<script setup lang="ts">
import { ref } from 'vue'
import { RouterView } from 'vue-router'
const num = ref(0)

const showConsole = () => {
  num.value++
  const dom = document.querySelector('#__vconsole')
  if (dom && num.value === 5) {
    dom.style.display = 'block'
  }
}
</script>

<template>
  <span id="console_log_debug" @click="showConsole"></span>
  <router-view v-slot="{ Component }">
    <component :is="Component" />
  </router-view>
</template>

<style>
#__vconsole {
  display: none;
}
#console_log_debug {
  position: fixed;
  z-index: 999;
  top: 0;
  left: 0;
  display: inline-block;
  width: 10px;
  height: 20px;
  opacity: 0;
}
</style>
