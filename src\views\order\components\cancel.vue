<template>
  <van-action-sheet v-model:show="show" title="选择取消原因" @cancel="cancel" teleport="body" class="i-sheet">
    <div class="sheet-wrapper">
      <van-radio-group v-model="checkedReason" class="sheet-radio-wrapper">
        <van-cell-group inset>
          <van-cell v-for="item in list" :key="item" :title="item" clickable @click.stop="checkedReason = item">
            <template #right-icon>
              <van-radio :name="item" />
            </template>
          </van-cell>
        </van-cell-group>
      </van-radio-group>

      <div class="bg-#F2F2F2 px-15 py-10 btn">
        <van-button type="primary" block round @click="submit">提交原因</van-button>
      </div>
    </div>
  </van-action-sheet>
</template>

<script setup lang="ts">
import { closeToast, showLoadingToast } from 'vant'
import { ref } from 'vue'

const show = ref(false)
const checkedReason = ref('不想要了')
const list = ['不想要了', '地址信息填写错误', '配送时间问题', '商品降价', '商品错选/多选', '物流无跟踪记录', '商品无货', '货物破损已拒签', '未收到货', '价格高于其他平台', '没用/少用/错用优惠']

defineExpose({
  show
})

const emits = defineEmits(['submit'])

const submit = () => {
  emits('submit', checkedReason.value)
  show.value = false
  checkedReason.value = '不想要了'
  showLoadingToast('处理中...')
  setTimeout(() => {
    closeToast()
  }, 1200)
}

const cancel = () => {
  show.value = false
  checkedReason.value = '不想要了'
}
</script>

<style scoped lang="scss">
.sheet-wrapper {
  display: flex;
  flex-direction: column;
  max-height: 70vh;
  overflow-y: hidden;
  position: relative;
  .sheet-radio-wrapper {
    flex: 1;
    overflow-y: auto;
  }
  .btn {
    flex-shrink: 0;
  }
}
//.sheet-radio-wrapper {
//  height: 420px;
//  padding: 12px 0;
//  overflow-y: auto;
//}
</style>

<style lang="scss">
.i-sheet .van-action-sheet__content {
  overflow: hidden;
}
</style>
