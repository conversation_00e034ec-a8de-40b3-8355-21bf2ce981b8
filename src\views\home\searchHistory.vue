<template>
  <div class="overflow-hidden">
    <NavBar ref="navBarRef" @onSearch="onSearch" @onCancel="onCancel" :searchPage="searchPage">
      <template #title>
        {{title}}
      </template>
    </NavBar>
    <History @search="searchHistory"/>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'HomeSearchHistory'
})
import { ref } from 'vue'
import { useRouter } from 'vue-router'

import NavBar from '@/components/navbar/searchBar.vue'
import History from '@/views/goods/history.vue'

const historyFlag = ref(true)
const searchPage = ref(true)
const router = useRouter()
const navBarRef = ref()
const title = ref('')

const onSearch = (val: string) => {
  router.replace({
    path: '/home/<USER>',
    query: {
      keyword: val
    }
  })
}

const onCancel = () => {
  historyFlag.value = false
}

const searchHistory = (val: string) => {
  router.replace({
    path: '/home/<USER>',
    query: {
      keyword: val,
      rand: Math.random()
    }
  })
}

</script>

<style scoped lang="scss"></style>
