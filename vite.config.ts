import { fileURLToPath, URL } from 'node:url'

import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import unocss from 'unocss/vite'
import { defineConfig, loadEnv } from 'vite'
import { createHtmlPlugin } from 'vite-plugin-html'

// https://vitejs.dev/config/
export default ({ mode }) => defineConfig({
  base: loadEnv(mode, process.cwd()).VITE_BASE_PATH_CONTEXT || '',
  plugins: [
    vue(),
    vueJsx(),
    unocss({
      configFile: './uno.config.ts'
    }),
    createHtmlPlugin({
      minify: true,
      pages: [
        {
          template: 'index.html',
          filename: 'index.html',
          injectOptions: {
            data: {
              title: '金采通一体化采购商城',
              buildTime: new Date().toLocaleString()
            }
          }
        }
      ]
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  css: {
    preprocessorOptions: {
      // 这里配置 mixin.scss 混合文件的全局引入
      scss: {
        additionalData: `@import "@/styles/mixin.scss";`
      }
    }
  },
  server: {
    host: '0.0.0.0'
  }
})

