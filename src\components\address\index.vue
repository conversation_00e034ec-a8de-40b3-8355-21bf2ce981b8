<template>
  <van-popup v-model:show="show" round position="bottom">
    <van-cascader
      v-model="cascaderValue"
      title="请选择所在地区"
      :options="options"
      :field-names="{ text: 'areaName', value: 'areaId', children: 'children' }"
      @close="show = false"
      @finish="onFinish"
      @change="onChange"
    />
  </van-popup>
</template>

<script setup lang="ts">
import type { AddressAreaRespVO } from '@/api/address'

defineOptions({
  name: 'AddressComp'
})
import {nextTick, ref} from 'vue'

import { getAreas } from '@/api/address'

const show = ref(false)
const selectedAddress = ref('')
const cascaderValue = ref('')
const addressArr = ref([])
// 选项列表，children 代表子选项，支持多级嵌套
const options = ref<AddressAreaRespVO[]>([])

const addTree = (selectedOptions, res, value) => {
  selectedOptions.forEach((item) => {
    if (item.areaId === value) {
      item.children = res.length ? res : null
    }
  })
}

// eslint-disable-next-line default-param-last
const getData = async (addressLevel = 'PROVINCE', areaId = '', selectedOptions = []) => {
  const res = await getAreas({ addressLevel: addressLevel, areaId: areaId })
  const data = ['CITY', 'COUNTRY'].includes(addressLevel) ? res.map((item) => ({ ...item, children: [] })) : res
  if(['北京', '天津', '上海', '重庆'].includes(selectedOptions[0].areaName) && addressLevel === 'COUNTRY') {
    data.forEach(item => {
      delete item.children
    })
  }
  addTree(selectedOptions, data, areaId)
}

const open = async (arr) => {
  show.value = true
  if (arr && arr.length > 0) {
    addressArr.value = arr
  }
  if (options.value.length === 0) {
    const res = await getAreas({ addressLevel: 'PROVINCE', areaId: '' })
    options.value = res.map((province) => ({ ...province, children: [] }))
    if (addressArr.value && addressArr.value.length > 0) {
      if(addressArr.value.length >= 1) {
        const citys = await getAreas({addressLevel: 'CITY', areaId: addressArr.value[0]})
        options.value.forEach((province) => {
          if (province.areaId === addressArr.value[0]) {
            province.children = citys
          }
        })
      }
      if(addressArr.value.length >= 2) {
        const areas = await getAreas({addressLevel: 'COUNTRY', areaId: addressArr.value[1]})
        options.value.forEach((province) => {
          if (province.areaId === addressArr.value[0]) {
            province.children.forEach((city) => {
              if (city.areaId === addressArr.value[1]) {
                city.children = areas
              }
            })
          }
        })
      }
      if(addressArr.value.length >= 3) {
        const towns = await getAreas({addressLevel: 'TOWN', areaId: addressArr.value[2]})
        options.value.forEach((province) => {
          if (province.areaId === addressArr.value[0]) {
            province.children.forEach((city) => {
              if (city.areaId === addressArr.value[1]) {
                city.children.forEach((area) => {
                  if (area.areaId === addressArr.value[2]) {
                    area.children = towns
                  }
                })
              }
            })
          }
        })
      }
      await nextTick()
      cascaderValue.value = addressArr.value[addressArr.value.length - 1]
    }
  }
}

const onChange = async ({ value, selectedOptions }) => {
  if (selectedOptions.length === 1) {
    await getData('CITY', value, selectedOptions)
  } else if (selectedOptions.length === 2) {
    await getData('COUNTRY', value, selectedOptions)
  } else if (selectedOptions.length === 3) {
    await getData('TOWN', value, selectedOptions)
  }
}

const emits = defineEmits(['selectedAddress'])
// 全部选项选择完毕后，会触发 finish 事件
const onFinish = ({ selectedOptions }) => {
  show.value = false
  // selectedAddress.value = selectedOptions.map((option) => option.text).join('/')
  selectedAddress.value = selectedOptions
  emits('selectedAddress', selectedAddress.value)
}

defineExpose({
  open,
  selectedAddress
})
</script>

<style scoped lang="scss"></style>