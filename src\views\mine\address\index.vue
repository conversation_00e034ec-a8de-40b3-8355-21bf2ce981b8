<template>
  <div class="pb-64">
    <NavBar title="收货地址设置"></NavBar>
    <div class="p-10">
      <template v-if="list.length">
        <div
          class="px-10 py-18 bg-white rounded-10 position-relative address"
          :class="{ active: item.defaulted }"
          :style="{ marginBottom: index === list.length - 1 ? '0' : '10px' }"
          v-for="(item, index) in list"
          :key="index"
          @click="setDefaultAddress(item)"
        >
          <div class="defaulted flex-center position-absolute top-[-1px] left-[-1px] text-12" v-if="item.defaulted">
            默认
          </div>
          <div class="flex items-center">
            <img :src="location" class="size-24" />
            <div class="ml-8 flex flex-col flex-1">
              <div class="text-black font-600">
                <span class="">{{ item.name }}</span>
                <span class="ml-6">{{ item.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') }}</span>
              </div>
              <div class="text-[#6C6C6C] text-12 font-600 mt-10">
                {{ getAddress(item) }}
              </div>
            </div>
            <img :src="arrowLeft" class="size-14 ml-14" @click.stop="editAddress(item)" />
          </div>
        </div>
      </template>
      <template v-else>
        <EmptyBox />
      </template>

      <div class="position-fixed bottom-0 left-0 w-full px-15 py-10 bg-[#F2F2F2]">
        <van-button block round class="btn-primary" @click="$router.push('/address/add')">新增收货地址</van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

defineOptions({
  name: 'Address'
})
import { showConfirmDialog } from 'vant'
import {useRoute, useRouter} from 'vue-router'

import { addressDelete, addressList, AppAddressRespVO } from '@/api/address'
import arrowLeft from '@/assets/order/arrow-left.png'
import location from '@/assets/order/location.png'
import EmptyBox from '@/components/emptyBox/index.vue'
import NavBar from '@/components/navbar/index.vue'
import { useUserStore } from '@/stores/user'

const list = ref<AppAddressRespVO[]>([])

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const setDefaultAddress = (item: AppAddressRespVO) => {
  userStore.setDefaultAddress(item)
  if (route.query.type === 'submit-order') {
    router.go(-1)
  }
}

const editAddress = (item: any) => {
  router.push({
    path: '/address/add',
    query: {
      id: item.id
    }
  })
}

const getAddress = (item: AppAddressRespVO) => {
  let name = item.provinceName + item.cityName
  if(item.countyName) {
    name += item.countyName
  }
  if(item.townName) {
    name += item.townName
  }
  name += item.consigneeAddress
  return name
}

const getList = () => {
  addressList().then((res) => {
    list.value = res || []
  })
}

const deleteAddress = (item: AppAddressRespVO) => {
  showConfirmDialog({
    title: '提示',
    message: '确认删除这个收货地址吗？'
  }).then(() => {
    addressDelete(item.id).then(() => {
      getList()
      if(item.defaulted) {
        userStore.loadUserAddress()
      }
    })
  }).catch(() => {
    // on cancel
  })
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.address {
  border: 2px solid #fff;
}
.active {
  border: 2px solid $primary-color;
}

.defaulted {
  background: $primary-color;
  width: 37px;
  height: 22px;
  color: #fff;
  border-top-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
</style>
