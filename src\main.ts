import './styles/main.scss'
import 'uno.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import '@/permission'

import App from './App.vue'
import router from './router'

import Vant from 'vant'
import 'vant/lib/index.css'

import Vconsole from 'vconsole'

if (import.meta.env.PROD) {
  const vConsole = new Vconsole()
}

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Vant)

app.mount('#app')
