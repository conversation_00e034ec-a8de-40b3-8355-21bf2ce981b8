import request from '@/utils/request'

const requestM = (data: any) =>
  request({
    ...data,
    ...{
      baseURL: import.meta.env.VITE_BASE_MEMBER_API
    }
  })

/**
 * AppAddressRespVO，用户 APP - 用户收件地址 Response VO
 */
export interface AppAddressRespVO {
  /**
   * 别名
   */
  alias?: string
  /**
   * 市编号
   */
  cityId: number
  /**
   * 市名称
   */
  cityName: string
  /**
   * 收件详细地址
   */
  consigneeAddress: string
  /**
   * 邮编
   */
  consigneeZip: string
  /**
   * 区县编号
   */
  countyId: number
  /**
   * 区县名称
   */
  countyName: string
  /**
   * 创建时间
   */
  createTime: Date
  /**
   * 是否默认地址
   */
  defaulted?: boolean
  detailAddress?: string
  /**
   * 邮箱
   */
  email?: string
  /**
   * 编号
   */
  id: number
  /**
   * 手机号
   */
  mobile: string
  /**
   * 收件人名称
   */
  name: string
  /**
   * 省编号
   */
  provinceId: number
  /**
   * 省名称
   */
  provinceName: string
  /**
   * 固定电话
   */
  telephone?: string
  /**
   * 乡镇编号
   */
  townId: number
  /**
   * 乡镇名称
   */
  townName: string
  [property: string]: any
}

/**
 * AddressAreaRespVO
 */
export interface AddressAreaRespVO {
  /**
   * 区域id
   */
  areaId?: number;
  /**
   * 区域名称
   */
  areaName?: string;
  [property: string]: any;
}

// 获取收货地址
export function addressList(): Promise<AppAddressRespVO> {
  return requestM({
    url: '/address/list',
    method: 'get'
  })
}

// 新增收货地址
export function addressCreate(data): Promise<AppAddressRespVO> {
  return requestM({
    url: '/address/create',
    method: 'post',
    data
  })
}

// 更新收货地址
export function addressUpdate(data): Promise<AppAddressRespVO> {
  return requestM({
    url: '/address/update',
    method: 'post',
    data
  })
}

// 删除用户收件地址
export function addressDelete(id: string | number): Promise<boolean> {
  return requestM({
    url: `/address/delete?id=${ id }`,
    method: 'post'
  })
}

// 获得默认的用户收件地址
export function addressDefault(): Promise<AppAddressRespVO> {
  return requestM({
    url: '/address/get-default',
    method: 'get'
  })
}

// 获得用户收件地址
export function addressGet(params: { id: string | number }): Promise<AppAddressRespVO> {
  return requestM({
    url: '/address/get',
    method: 'get',
    params
  })
}

// 获取省市区地址
export function getAreas(data: {
  addressLevel: string | number
  areaId: string | number
}): Promise<AddressAreaRespVO[]> {
  return requestM({
    url: '/address/getAreas',
    method: 'post',
    data
  })
}

// 智能识别地址
export function parseAddress(data): Promise<AppAddressRespVO> {
  return requestM({
    url: '/address/parse-address',
    method: 'post',
    data
  })
}