<template>
  <van-sticky>
    <van-nav-bar v-bind="$attrs" class="search-bar">
      <template #left>
        <img v-if="!searchFlag" @click="clickLeft" :src="back" class="size-20" />
        <van-search
          v-if="searchFlag"
          class="search"
          v-model="value"
          show-action
          placeholder="搜索商品"
          @search="onSearch"
        >
          <template #action>
            <van-button type="danger" size="mini" round @click="onSearchBtn">搜索</van-button>
          </template>
        </van-search>
      </template>
      <template #right>
        <div class="flex items-center right text-black">
          <img v-if="!searchFlag" :src="search" class="w-20 h-20" @click="searchFlag = !searchFlag" />
          <span v-if="searchFlag" @click="cancel">取消</span>
          <span v-if="manageFlag" class="ml-10" @click="onManager">{{ !manager ? '管理' : '完成' }}</span>
        </div>
      </template>
      <template #title v-if="$slots.title">
        <slot v-if="!searchFlag" name="title"></slot>
      </template>
    </van-nav-bar>
  </van-sticky>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

import back from '@/assets/back.png'
import search from '@/assets/shoppingCart/search.png'

defineOptions({
  name: 'SearchBar'
})

const props = defineProps({
  manageFlag: {
    type: Boolean,
    default: false
  },
  searchPage: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['update:manager', 'onSearch', 'onCancel'])

const manager = ref(false)
const searchFlag = ref(false)
const value = ref('')

defineExpose({
  value
})

if (props.searchPage) {
  searchFlag.value = true
}
const onSearch = (val: string) => {
  emits('onSearch', val)
}

const onSearchBtn = () => {
  emits('onSearch', value.value)
}

const onManager = () => {
  manager.value = !manager.value
  emits('update:manager', manager.value)
}

const router = useRouter()
const clickLeft = () => {
  if (!searchFlag.value) {
    router.go(-1)
  }
}

const cancel = () => {
  searchFlag.value = !searchFlag.value
  emits('onCancel', true)
}
</script>

<style scoped lang="scss">
.van-nav-bar {
  background: #f2f2f2;
  height: 48px;
}
.search-bar {
  :deep(.van-nav-bar__left) {
    width: calc(100% - 74px);
  }
  .search {
    position: relative;
    border-radius: 20px;
    padding: 0 4px 0 12px;
    width: 100%;
    height: 34px;
    line-height: 34px;
  }
  :deep(.van-search__content) {
    background-color: #fff;
    padding-left: 0;
    border-radius: 20px;
  }
  :deep(.van-search__action) {
    position: absolute;
    right: 8px;
    top: -5px;
    width: 50px;
    padding: 0;
    button {
      width: 100%;
      line-height: 24px;
      height: 24px;
    }
  }
  :deep(.van-icon-search:before) {
    color: #C8C8C8;
  }
}

:deep(.van-nav-bar__title) {
  color: #000;
  font-size: 17px;
  font-weight: 600;
}
</style>
