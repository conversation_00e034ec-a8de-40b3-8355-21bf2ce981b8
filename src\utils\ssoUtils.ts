import { getSsoLogoutInfo, getSsoLogoutInfoV2 } from '@/api/login'
import router from '@/router/index'
import { useHomeStore } from '@/stores/home'
import { removeToken } from '@/utils/auth'

/**
 * SSO-CAS登录地址处理，增加redirect
 * @param {*} originUrl
 * @returns
 */
export const appendRedirect4LoginUrl = (originUrl: string, extraPath: string) => {
  const url = new URL(originUrl)
  const serviceUrl = url.searchParams.get('service')
  if(!serviceUrl) {
    return originUrl
  }
  const decodedServiceUrl = decodeURIComponent(serviceUrl);
  let extra = ''
  if(decodedServiceUrl.indexOf('?') < 0) {
    extra += '?'
  } else {
    extra += '&'
  }
  if(extraPath) {
    extra += `redirect=${  encodeURIComponent(extraPath) }`
  }
  url.searchParams.set('service', serviceUrl + extra)
  console.log('url2=', url.href)
  return url.href
}

export const formatDateTime = (date: any, format: string) => {
  const o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 小时
    'H+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
    a: date.getHours() < 12 ? '上午' : '下午', // 上午/下午
    A: date.getHours() < 12 ? 'AM' : 'PM', // AM/PM
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (`${ date.getFullYear()  }`).substr(4 - RegExp.$1.length));
  }
  for (const k in o) {
    if (new RegExp(`(${  k  })`).test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : (`00${  o[k] }`).substr((`${  o[k] }`).length)
      );
    }
  }

  return format;
}

/**
 * 登录跳转处理
 * type: 1-内部登录
 *       2-自动判断
 * @param {*} type
 */
export const toLogin = (type = 2, returnPath = '') => {
  removeToken()
  const currentRoutePath = router.currentRoute?.value.fullPath || ''
  let backPath = returnPath || currentRoutePath
  if(backPath.indexOf('/login') >= 0) {
    backPath = ''
  }

  const useHome = useHomeStore()
  const configData = useHome.configInfo.configData || {}
  console.log('currentRoutePath===', backPath, type, configData.loginType)
  if(type === 1 || configData.loginType === 1) {
    router.push({ path: '/login/index', query: {redirect: backPath}});
  } else {
    window.location.href = appendRedirect4LoginUrl(configData.loginUrl, backPath)
  }
}

/**
 * 退出跳转处理, 根据登录方式自动判断退出方式
 * @param {*} type
 */
export const toLogout = () => {
  removeToken()
  const loginWay = sessionStorage.getItem('loginWay') || 'account'
  if(loginWay === 'account') {
    console.log('logout...')
    router.push("/home")
    return
  }

  const useHome = useHomeStore()
  const configData = useHome.configInfo.configData || {}
  let logoutFunc = null
  const lgoutParams = {
    redirect: ''
  }
  if(configData.logoutType === 20) {
    logoutFunc = getSsoLogoutInfo
  } else if(configData.logoutType === 25) {
    logoutFunc = getSsoLogoutInfoV2
  } else {
    return
  }
  logoutFunc(lgoutParams).then(res => {
    window.location.href = res
  })
}