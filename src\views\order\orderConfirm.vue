<template>
  <div>
    <van-sticky>
      <NavBar title="订单详情"></NavBar>
    </van-sticky>

    <div class="p-10 mb-56">
      <div class="px-10 py-18 bg-white rounded-10" @click="toLogistics">
        <div class="flex items-center">
          <img :src="location" class="size-24" />
          <div class="ml-8 flex flex-col flex-1">
            <div class="text-black font-600">
              <span class="">{{ addressDefault.name }}</span>
              <span class="ml-6">{{ addressDefault.mobile?.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') }}</span>
            </div>
            <div class="text-[#6C6C6C] text-12 font-600 mt-10">{{ getAddress() }}</div>
          </div>
          <img :src="arrowLeft" class="size-14 ml-14" />
        </div>
      </div>

      <div
        class="px-10 py-12 bg-white rounded-10 mt-10"
        v-for="(item, index) in shopInfo.detailRespVO.shopItems"
        :key="index"
      >
        <div class="text-black font-600">{{ item.supplierName }}</div>
        <div class="flex items-start justify-between mt-12" v-for="(sku, idx) in item.skuItems" :key="idx">
          <div class="flex items-start">
            <img :src="sku.picUrl" class="size-94 mr-10" />
            <div class="flex flex-col">
              <div class="text-black font-600 ellipsis-2">{{ sku.skuName }}</div>
              <div class="mt-9 mb-17">
                <!--                <span class="spec px-8 py-4 text-[#6c6c6c] rounded-10 bg-[#F2F2F2] text-12">黑胡椒味400g</span>-->
              </div>
              <div class="text-[#333333] text-12 font-600 flex items-center justify-start">￥{{ sku.skuPrice }}
                <div class="ml-10 text-[#dc4141] text-12 font-600" v-if="!sku.stock || isOutStock(sku.stock.stockStateType)">无货</div>
              </div>
            </div>
          </div>
          <div class="text-[#6D6C6C] text-12 ml-32">x{{ sku.count }}</div>
        </div>
        <div class="flex items-center justify-between mt-15">
          <div class="text-black">商品金额</div>
          <div class="text-black font-500">¥{{ formatMoney(item.shopSkuPrice) }}</div>
        </div>
        <div class="flex items-center justify-between mt-10">
          <div class="text-black">运费</div>
          <div class="text-black font-500" v-if="item.shopFreight">{{ formatMoney(item.shopFreight) }}(满{{ getSupplierFreightThreshold(item.supplierId) }}包邮)</div>
          <div class="text-black font-500" v-else>包邮</div>
        </div>
      </div>

      <div class="px-10 py-20 bg-white rounded-10 mt-10">
        <div class="flex items-center justify-between mb-20">
          <div class="text-black">支付方式</div>
          <div class="text-black">
            <van-radio-group v-model="paymentType" direction="horizontal">
              <van-radio v-for="(item, index) in paymentTypeList" :key="index" :name="item.value" :disabled="item.disabled">{{
                item.label
              }}</van-radio>
            </van-radio-group>
          </div>
        </div>
        <div class="flex items-center justify-between" v-for="(item, index) in shopInfo.scoreDetail" :key="index">
          <div class="text-black">{{ item.scoreType == 0 ? '福利' : '扶贫' }}可用积分</div>
          <div class="text-black font-500" v-if="item.availableScore">{{ formatMoney(item.availableScore) }}</div>
          <div class="text-red font-500" v-else>0</div>
        </div>
      </div>

      <div class="px-10 py-20 bg-white rounded-10 mt-10">
        <!-- <div class="flex items-center justify-between mb-20">
          <div class="text-black">配送服务</div>
          <div class="text-black font-600">快递免邮，付款后3天发货</div>
        </div> -->
        <div class="flex items-center justify-between">
          <div class="text-black">订单备注</div>
          <div class="text-[#989898] flex items-center" @click="showRemarkHandle">
            <span
              class="w-152 ellipsis-1 text-right"
              :style="{ color: remark ? '#000' : '#989898', fontWeight: remark ? '600' : '400' }"
              >{{ remark || '无备注' }}</span
            >
            <img :src="arrowLeft" class="size-14 ml-5" />
          </div>
        </div>
      </div>

      <div class="px-10 py-20 bg-white rounded-10 mt-10 lh-14">
        <div class="flex items-center justify-between mb-20">
          <div class="text-black">商品总额</div>
          <div class="flex items-center">
            <div class="text-[#989898] mr-5">商品总价</div>
            <div class="ml-5 text-black font-500">¥{{ formatMoney(shopInfo.detailRespVO.payPrice) }}</div>
          </div>
        </div>
        <div class="flex items-center justify-between mb-20">
          <div class="text-black">运费</div>
          <div class="flex items-center">
            <div class="text-[#989898] mr-5">运费（快递）</div>
            <div class="ml-5 text-black font-500">¥{{ formatMoney(shopInfo.totalFreight) }}</div>
          </div>
        </div>
        <div v-if="showTradeDiv">
          <div class="flex items-center justify-between mb-20" v-for="(item, index) in shopInfo.scoreDetail" :key="index">
            <div class="text-black">{{item.scoreType == 0 ? '福利' : '扶贫' }}积分抵扣</div>
            <div class="ml-5 text-black font-500">¥{{ formatMoney(item.payScore) }}</div>
          </div>
          <div class="flex items-center justify-between">
            <div class="text-black">合计</div>
            <div class="color-primary font-600 text-17">¥{{ formatMoney(shopInfo.totalPayPrice) }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="position-fixed left-0 bottom-0 bg-white pt-12 px-10 lh-16 flex items-center justify-between w-full van-safe-area-bottom">
      <div class="pb-10">共计 {{ shopInfo.detailRespVO.count }} 件</div>
      <div class="flex items-center pb-10">
        <div  v-if="showTradeDiv">
          <div class="text-black font-600 mr-7">支付金额</div>
          <div class="color-primary font-600 flex items-center">
            <span class="text-12">¥</span>
            <span class="text-17">{{ formatMoney(shopInfo.totalRemainingAmount | 0) }}</span>
          </div>
        </div>
        <div class="ml-7" v-if="showSubmit">
          <van-button round class="w-88 !h-31 btn-primary" :loading="submitLoading" @click="submitOrder" :disabled="!orderValid">提交</van-button>
        </div>
      </div>
    </div>

    <Payment v-model:show="showPayment" />

    <van-action-sheet v-model:show="showRemark" title="订单备注" :closeable="false">
      <div class="content">
        <img :src="close" class="size-18 position-absolute top-10 right-10" @click="showRemark = false" />
        <van-field
          v-model="orderDesc"
          rows="5"
          :autosize="{ maxHeight: 109 }"
          label=""
          :border="false"
          type="textarea"
          maxlength="200"
          placeholder="选填，请先跟商家协商一致。付款后商家可见"
          show-word-limit
        />
        <div class="p-15 pb-17 mt-39">
          <van-button round block class="btn-primary" @click="save">确定</van-button>
        </div>
      </div>
    </van-action-sheet>

    <van-toast v-model:show="saleAmountMinTip" style="padding: 20">
      <template #message>
        <div v-for="msg in saleAmountMinErrors" :key="msg">{{ msg }}</div>
      </template>
    </van-toast>

  </div>
</template>

<script setup lang="ts">
import { closeToast, showLoadingToast, showToast } from 'vant'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { orderCreate } from "@/api/order"
import { cartCheckOrder, CartDetailRespVO } from '@/api/shoppingCart'
import { userAccount } from '@/api/user'
import arrowLeft from '@/assets/order/arrow-left.png'
import location from '@/assets/order/location.png'
import close from '@/assets/shoppingCart/close.png'
import NavBar from '@/components/navbar/index.vue'
import { useHomeStore } from '@/stores/home'
import { useUserStore } from '@/stores/user'
import { formatMoney } from '@/utils/base'
import { isOutStock } from '@/utils/productUtils'
import Payment from '@/views/order/payment.vue'
import { watch } from 'node:fs'

const remark = ref('')
const orderDesc = ref('')
const showRemark = ref(false)
const showPayment = ref(false)
const paymentType = ref()
const submitLoading = ref(false)

const homeStore = useHomeStore()
const userStore = useUserStore()
const configData = homeStore.configInfo.configData || {}
const supplierList = homeStore.configInfo.supplierList

const showSubmit = computed(() => configData.orderSwitch || configData.orderSwitch === undefined)

const shopInfo = ref<CartDetailRespVO>({
  detailRespVO: {
    count: 0,
    payPrice: 0,
    selectedCount: 0,
    shopItems: []
  },
  totalPayPrice: 0,
  totalFreight: 0
})

const getSupplierFreightThreshold = (sid: any) => {
  const supplier = supplierList.find(item => item.id === sid)
  if(supplier) {
    return supplier.freightThreshold || 0
  }

  return null
}

const getSupplierSaleAmountMin = (sid: any) => {
  const supplier = supplierList.find(item => item.id === sid)
  if(supplier) {
    return supplier.saleAmountMin || 0
  }

  return null
}

const saleAmountMinTip = ref(false)
const saleAmountMinErrors = ref([])
const validateOrderSaleAmountMin = () => {
  if(shopInfo.value.detailRespVO.shopItems) {
    saleAmountMinErrors.value = []
    saleAmountMinTip.value = false
    shopInfo.value.detailRespVO.shopItems.forEach(shop => {
      const saleAmountMin = getSupplierSaleAmountMin(shop.supplierId)
      if(shop.shopSkuPrice < saleAmountMin) {
        saleAmountMinErrors.value.push(`不满足${ shop.supplierName }的起售金额￥${ saleAmountMin }`)
      }
    })
    if(saleAmountMinErrors.value.length) {
      saleAmountMinTip.value = true
      return false
    }
  }

  return true
}

const showRemarkHandle = () => {
  showRemark.value = true
  orderDesc.value = remark.value
}

const save = () => {
  remark.value = orderDesc.value
  showRemark.value = false
}

const router = useRouter()
const toLogistics = () => {
  router.push({
    path: '/address/index',
    query: {
      type: 'submit-order'
    }
  })
}

const userAccountInfo = ref({})
const getUserAccount = () => {
  userAccount().then((res) => {
    userAccountInfo.value = res || {}
    paymentType.value = paymentTypeList.value[0]?.value
  })
}

const addressDefault = computed(() => userStore.defaultAddress)
const orderValid = ref(false)

const checkItemOutStock = (detailRespVO) => {
  let flag = false
  if(detailRespVO && detailRespVO.shopItems) {
    detailRespVO.shopItems.forEach(shopItem => {
      if(flag) {
        return
      }
      if(shopItem.skuItems) {
        shopItem.skuItems.forEach(skuItem => {
          if(flag) {
            return
          }
          flag = !skuItem.stock || isOutStock(skuItem.stock.stockStateType)
          if(flag) {
            showToast(`商品${ skuItem.skuName }无货，请您稍后重试或选择其它商品`)
          }
        })
      }
    })
  }

  return flag
}

const getCartCheckOrder = (params: any) => {
  let paramsData = {
    addressId: addressDefault.value.id as number
  }
  if (params.skuId) {
    paramsData = {
      addressId: params.addressId,
      item: {
        count: query.count || 1,
        skuId: params.skuId,
        supplierId: params.supplierId
      }
    }
  }
  showLoadingToast({
    duration: 0,
    forbidClick: true,
    message: '加载中...'
  })
  cartCheckOrder(paramsData).then((res) => {
    closeToast()
    shopInfo.value = {
      detailRespVO: res.detailRespVO,
      totalPayPrice: res.totalPayPrice,
      totalFreight: res.totalFreight,
      totalRemainingAmount: res.totalRemainingAmount,
      scoreDetail: res.scoreDetail,
      canPurchase: res.canPurchase,
      mixPay: false
    }
    res.scoreDetail.forEach(item => {
      if(item.scoreType === 0 && item.payPrice !== 0) {
        shopInfo.value.mixPay = true
      }
    })

    if(res.totalFreight > 0) {
      showToast('由于订单未达到包邮金额 ，若此单商品需要退款产生的运费将无法退还，烦请知悉！')
    }
    orderValid.value = true
    if(!res.detailRespVO.shopItems || !res.detailRespVO.shopItems.length) {
      showToast('订单商品为空，无法提交')
      orderValid.value = false
      return
    }
    if(checkItemOutStock(res.detailRespVO)) {
      orderValid.value = false
    }
  })
}

const getAddress = () => {
  const item = addressDefault.value
  if (item.provinceName) {
    return `${ item.provinceName }${ item.cityName }${ item.countyName }${ item.townName }${ item.consigneeAddress }`
  }
  return ''
}

const submitOrder = () => {
  if(!validateOrderSaleAmountMin()) {
    return
  }
  if(!addressDefault.value.id) {
    showToast('请选择收货地址')
    return
  }
  if(!paymentType.value) {
    showToast('请选择支付方式')
    return
  }
  submitLoading.value = true
  orderCreate({
    timestamp: new Date().getTime(),
    addressId: addressDefault.value.id as number,
    paymentMethod: paymentType.value,
    remark: orderDesc.value,
    fromCart: true,
    shopItems: shopInfo.value.detailRespVO.shopItems.map(item => ({
      supplierId: item.supplierId,
      items: item.skuItems.map(p => ({
        skuId: p.skuId,
        count: p.count,
        supplierId: p.supplierId
      }))
    }))
  }).then((res) => {
    showToast('提交订单成功')
    userStore.loadCartCount()
    const query = {
      no: res.no,
      oType: 0
    }
    if(res.subOrders && res.subOrders.length > 1) {
      query.oType = 1
    }
    let routePath = '/order/orderSuccess'
    if ([5,7].includes(paymentType.value)) {
      routePath = '/order/pay'
    }
    router.replace({
      path: routePath,
      query: query
    })
  }).finally(() => {
    submitLoading.value = false
  })
}

const paymentMethodList = computed(() => {
  let payMethods= configData.payMethod || ''
  payMethods = payMethods.split(',') || []
  payMethods = payMethods.filter((item:string) => !!item).map((item:string) => parseInt(item))
  return payMethods
})

const paymentTypeList = computed(() => {
  const arr = []
  if(paymentMethodList.value.includes(5)) {
    arr.push({label: '线上支付', value: 5, disabled: false})
  }
  if(paymentMethodList.value.includes(7) && shopInfo.value.mixPay && shopInfo.value.canPurchase) {
    arr.push({label: '混合支付', value: 7, disabled: false})
  }
  if(paymentMethodList.value.includes(6) && (shopInfo.value.canPurchase && !shopInfo.value.mixPay)) {
    arr.push({label: '积分支付', value: 6, disabled: shopInfo.value.mixPay})
  }

  return arr
})

const showTradeDiv = computed(() => {
  return true
})

const route = useRoute()
const query = route.query
onMounted(() => {
  getUserAccount()
  getCartCheckOrder({
    skuId: query.skuId,
    supplierId: query.supplierId,
    addressId: query.id
  })
})

// 监听paymentTypeList变化，设置默认支付方式
watch(paymentTypeList, (newList) => {
  if (newList.length > 0 && !paymentType.value) {
    paymentType.value = newList[0].value
  }
}, { immediate: true })
</script>

<style scoped lang="scss">
.content {
  :deep(.van-field__control) {
    background: #f2f2f2;
    border-radius: 5px;
    padding: 14px;
  }
}

:deep(.van-radio) {
  &:last-child {
    margin-right: 0;
  }
  .van-radio__icon--dot__icon {
    height: calc(100% - 4px);
    width: calc(100% - 4px);
  }
  .van-radio__label {
    line-height: 14px;
  }
}
</style>
