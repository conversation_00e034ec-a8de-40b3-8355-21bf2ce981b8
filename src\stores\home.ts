import { defineStore } from 'pinia'
import { showFailToast } from 'vant'
import { ref } from 'vue'

import type { configInfoType, homeConfigType, styleConfigType } from '@/api/home'
import { getConfig, getHomeConfig, getTopList } from '@/api/home'

export const useHomeStore = defineStore('home', () => {
  const cacheKey = ref(1000)
  const configInfo = ref({
    configData: {} as configInfoType,
    supplierList: [],
    homeConfig: {} as homeConfigType,
    styleConfig: {} as styleConfigType
  })

  const updateCacheKey = () => {
    cacheKey.value += 1
  }

  const setConfigData = (data) => {
    if (data && data.tenantId) {
      configInfo.value.configData = data

      if(configInfo.value.configData.title) {
        const title = document.createElement('title');
        title.innerText = configInfo.value.configData.title;
        document.getElementsByTagName('head')[0].appendChild(title);
      }
    }

    return true
  }

  const setHomeConfig = (data) => {
    configInfo.value.homeConfig = data || {}
  }

  const setStyleConfig = (data) => {
    configInfo.value.styleConfig = data || {}

    const faviconUrl = configInfo.value.styleConfig.faviconUrl
    if(faviconUrl && faviconUrl.length > 5) {
      const link = document.querySelector("link[rel*='icon']") || document.createElement('link');
      link.type = 'image/x-icon';
      link.rel = 'shortcut icon';
      link.href = `${ faviconUrl  }?rn=${  new Date().getTime() }`;
      document.getElementsByTagName('head')[0].appendChild(link);
    }
    document.querySelector(':root').style.cssText = configInfo.value.styleConfig.diyCss
  }

  const getConfigData = async () => {
    let host = location.host || ''
    if (import.meta.env.DEV) {
      host = import.meta.env.VITE_BASE_TENANT_HOST
    }
    const params = { host }
    const cacheKey = 'jcymallh5-basisconfig'
    const cacheVal = localStorage.getItem(cacheKey)
    if (cacheVal) {
      setConfigData(JSON.parse(cacheVal))
    }
    let data = null
    try {
      data = await getConfig(params)
    } catch(e) {
      console.error('商城基础配置异常，请联系管理员')
      return false
    }
    if (data && data.tenantId) {
      data.inited = true
      setConfigData(data)
      localStorage.setItem(cacheKey, JSON.stringify(data))
      await loadSupplierList()
      return true
    }
    console.log('商城基础配置异常，请联系管理员')
    return false
  }

  const loadHomeConfig = async () => {
    const cacheHomeKey1 = 'jcymallh5-homeconfig-v2'
    const cacheHomeKey2 = 'jcymallh5-styleconfig-v2'
    // 首页配置一般变化频率很低，加上缓存后，页面刷新或进入时能减少页面样式过渡及闪烁
    const cacheHomeVal1 = localStorage.getItem(cacheHomeKey1)
    const cacheHomeVal2 = localStorage.getItem(cacheHomeKey2)
    if(cacheHomeVal1) {
      setConfigData(JSON.parse(cacheHomeVal1))
    }
    if(cacheHomeVal2) {
      setStyleConfig(JSON.parse(cacheHomeVal2))
    }
    const params = { platform: 'h5' }
    try {
      const data = await getHomeConfig(params)
      data.forEach(item => {
        if (item.type === 20) {
          setHomeConfig(JSON.parse(item.content))
          localStorage.setItem(cacheHomeKey1, item.content)
        }
        if (item.type === 21) {
          setStyleConfig(JSON.parse(item.content))
          localStorage.setItem(cacheHomeKey2, item.content)
        }
      })
      return true
    } catch(e) {
      showFailToast('商城首页配置异常，请联系管理员')
      return false
    }
  }

  const loadSupplierList = async() => {
    const data = await getTopList()
    configInfo.value.supplierList = data || []
  }

  return { configInfo, getConfigData, loadHomeConfig, cacheKey, updateCacheKey }
})

