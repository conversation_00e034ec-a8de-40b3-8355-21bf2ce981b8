<template>
  <div class="mb-10">
    <van-sticky>
      <NavBar title="售后管理" class="!bg-white" />
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" loading-text="加载中..." :finished="finished" finished-text="没有更多了" @load="onLoad">
        <div
          class="p-10 bg-white rounded-10 mx-10 mt-10"
          v-for="(child, idx) in list"
          :key="idx"
        >
          <OGoods :goods="child" show-bottom after-sales />
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'AfterSales'
})
import { ref } from 'vue'

import NavBar from '@/components/navbar/index.vue'
import OGoods from '@/views/order/components/oGoods.vue'

const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)

const props = defineProps({
  orderType: {
    type: String,
    default: 'all'
  }
})

const list = ref([
  {
    name: '百事可乐无糖Pepsi X KARL LAGERFELD 老佛爷联盟限量罐',
    price: 134.24,
    amount: 1,
    checked: true
  },
  {
    name: '百事可乐无糖Pepsi X KARL LAGERFELD 老佛爷联盟限量罐',
    price: 134.24,
    amount: 1,
    checked: true
  },
  {
    name: '百事可乐无糖Pepsi X KARL LAGERFELD 老佛爷联盟限量罐',
    price: 134.24,
    amount: 1,
    checked: true
  },
  {
    name: '百事可乐无糖Pepsi X KARL LAGERFELD 老佛爷联盟限量罐',
    price: 134.24,
    amount: 1,
    checked: true
  }
])

const onLoad = () => {
  setTimeout(() => {
    if (refreshing.value) {
      list.value = []
      refreshing.value = false
    }

    for (let i = 0; i < 10; i++) {
      list.value.push({
        name: '百事可乐无糖Pepsi X KARL LAGERFELD 老佛爷联盟限量罐',
        price: 134.24,
        amount: 1,
        checked: true
      })
    }
    loading.value = false

    if (list.value.length >= 40) {
      finished.value = true
    }
  }, 1000)
}

const onRefresh = () => {
  // 清空列表数据
  finished.value = false

  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true
  onLoad()
}
</script>

<style scoped lang="scss"></style>
