<template>
  <div class="brand p-12 mt-8">
    <div class="brand__item" v-for="(item, index) in supplierList" :key="index" @click="toSupplier(item.id)" :style="getStyle(index + 1)">
      <van-image :src="item.logoUrl" class="brand__item-img h-36 w-72 mr-4" />
      <div class="text">{{ item.name }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

import p1 from '@/assets/home/<USER>'
import p2 from '@/assets/home/<USER>'
import p3 from '@/assets/home/<USER>'
import { useHomeStore } from '@/stores/home';

defineOptions({
  name: 'Supplier'
})

const useHome = useHomeStore()
const supplierList = useHome.configInfo.supplierList

const getStyle = (index: number) => {
  if (index % 3 === 1) {
    return `background: url(${ p1 }) no-repeat center center / 100% 100%;`
  } else if (index % 3 === 2) {
    return `background: url(${ p2 }) no-repeat center center / 100% 100%;`
  } else if (index % 3 === 0) {
    return `background: url(${ p3 }) no-repeat center center / 100% 100%;`
  }
}

const router = useRouter()
const toSupplier = (supplierId: string) => {
  router.push({
    path: '/home/<USER>',
    query: {
      supplierId
    }
  })
}
</script>

<style scoped lang="scss">
.brand {
  width: 375px;
  display: flex;
  padding: 12px;
  background-color: #fff;
  overflow-x: auto;
  .brand__item {
    height: 60px;
    padding: 8px 4px 8px 8px;
    display: flex;
    align-items: center;
    margin-right: 8px;
    background-color: #edba5b;
    border-radius: 4px;
    .text {
      font-size: 12px;
      font-weight: 500;
      width: 50px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      text-wrap: nowrap;
    }
  }

  .brand__item-img {
    border-radius: 4px;
    overflow: hidden;
  }
}
</style>
