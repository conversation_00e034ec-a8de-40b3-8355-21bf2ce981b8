import request from '@/utils/request'

const requestT = (data: any) => request({
  ...data,
  ...{
    baseURL: import.meta.env.VITE_BASE_TRADE_API
  }
})

export interface ShoppingCart {
  /**
   * 市编号
   */
  cityId?: number
  /**
   * 区县编号
   */
  countyId?: number
  /**
   * 省编号
   */
  provinceId?: number
  /**
   * 乡镇编号
   */
  townId?: number
  [property: string]: any
}

/**
 * CommonResultCartDetailRespVO
 */
export interface Response {
  code?: number
  // eslint-disable-next-line no-use-before-define
  data?: CartDetailRespVO
  msg?: string
  [property: string]: any
}

/**
 * CartDetailRespVO
 */
export interface CartDetailRespVO {
  count?: number
  payPrice?: number
  selectedCount?: number
  // eslint-disable-next-line no-use-before-define
  shopItems?: CartShopItem[]
  [property: string]: any
}

/**
 * 店铺供应商信息
 *
 * CartShopItem
 */
export interface CartShopItem {
  /**
   * 店铺商品总数
   */
  count?: number
  /**
   * Logo地址
   */
  logoUrl?: string
  /**
   * 店铺选中商品总数
   */
  selectedCount?: number
  /**
   * 店铺运费
   */
  shopFreight?: number
  /**
   * 店铺id
   */
  shopId?: number
  /**
   * 店铺名称
   */
  supplierName?: string
  /**
   * 店铺商品总价
   */
  shopSkuPrice?: number
  /**
   * 商品信息
   */
  // eslint-disable-next-line no-use-before-define
  skuItems?: CartSkuItem[]
  [property: string]: any
}

/**
 * CartSkuItem
 */
export interface CartSkuItem {
  /**
   * 是否可购买
   */
  canPurchase: boolean;
  /**
   * 商品数量
   */
  count: number;
  /**
   * 主键
   */
  id: number;
  /**
   * 最低起购量
   */
  lowestBuy?: number;
  notCanPurchaseReason?: string;
  /**
   * 图片地址
   */
  picUrl?: string;
  /**
   * 是否选中
   */
  selected: boolean;
  /**
   * 商品id
   */
  skuId?: number;
  /**
   * 供应商商品id
   */
  skuInnerId?: string;
  /**
   * 商品 SKU 名字
   */
  skuName: string;
  /**
   * 商品单价
   */
  skuPrice?: number;
  /**
   * 商品总价
   */
  skuTotalPrice?: number;
  /**
   * 商品状态 1上架 0下架
   */
  status?: number;
  // eslint-disable-next-line no-use-before-define
  stock?: Stock;
  /**
   * 供应商id
   */
  supplierId?: number;
  [property: string]: any;
}

/**
 * 库存
 *
 * Stock
 */
export interface Stock {
  /**
   *
   * 剩余数量。当此值为-1时，为未查询到。StockStateDesc为33：入参的skuNums字段，skuId对应的num小于50，此字段为实际库存。入参的skuNums字段，skuId对应的num大于等50于并小于100，此字段为-1。入参的skuNums字段，skuId对应的num大于100，此字段等于num。(此种情况并未返回真实京东库存)
   */
  remainNumInt?: number
  /**
   * 商品id
   */
  skuId?: string
  /**
   * 库存状态描述。以下为stockStateId不同时，此字段不同的返回值： 33 有货 现货-下单立即发货 39 有货 在途-正在内部配货，预计2\\\\x7e6天到达本仓库 40
   * 有货 可配货-下单后从有货仓库配货 36 预订 34 无货 99 无货开预定
   */
  stockStateDesc?: string
  /**
   * 库存状态类型，参考枚举值： 33,39,40,36,34,99
   */
  stockStateType?: number
  [property: string]: any
}

/**
 * CartDetailRespVO，购物车信息
 */
export interface CartDetailRespVO {
  /**
   * 商品总数
   */
  count?: number;
  /**
   * 应付金额（总）
   */
  payPrice: number;
  /**
   * 选中商品数量
   */
  selectedCount?: number;
  /**
   * 店铺商品信息
   */
  shopItems?: CartShopItem[];
  /**
   * 订单总费用
   */
  totalPayPrice?: number;
  [property: string]: any;
}


// 查询购物车数据
export function getShoppingCartList(params?: ShoppingCart): Promise<CartDetailRespVO> {
  return requestT({
    url: '/cart/get-detail',
    method: 'get',
    params
  })
}

// 加入购物车
export function addCartApi(data: any) {
  return requestT({
    url: '/cart/add-count',
    method: 'post',
    data
  })
}

// 查询用户在购物车中的商品数量
export function getCartCount() {
  return requestT({
    url: '/cart/get-count',
    method: 'get'
  })
}

// 更新购物车商品是否选中
export function cartUpdateSelected(data: { skuIds: (string | undefined)[] | undefined; selected: boolean }) {
  return requestT({
    url: '/cart/update-selected',
    method: 'post',
    data
  })
}

// 删除购物车商品
export function cartDelete(skuIds: string | undefined) {
  return requestT({
    url: `/cart/delete?skuIds=${ skuIds }`,
    method: 'post'
  })
}

// 更新购物车商品数量
export function cartUpdateCount(data: {
  skuId: (string | undefined) | undefined
  count: number | undefined
  area?: { provinceId?: number; cityId?: number; countyId?: number; townId?: number }
}) {
  return requestT({
    url: '/cart/update-count',
    method: 'post',
    data
  })
}

// 福利和扶贫商品检查
export function checkMix() {
  return requestT({
    url: '/cart/check-mix',
    method: 'get',
  })
}

// 订单检查页
export function cartCheckOrder(data: { addressId: number }) {
  return requestT({
    url: '/cart/check-order',
    method: 'post',
    data
  })
}
