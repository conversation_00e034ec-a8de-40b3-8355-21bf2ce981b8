<template>
  <div>
    <div class="mb-60">
      <van-sticky>
        <NavBar title="订单详情" class="!bg-white" @clickLeft="router.push('/order/index')" />
        <div class="payType w-full h-100 px-20 py-26 flex items-center">
          <img :src="money" class="size-48" />
          <div class="flex flex-col ml-10 text-white">
            <div class="font-600 text-20 lh-20">{{ getStatusLabel(orderInfo)}}</div>
<!--            <div class="lh-14 mt-9">剩余 23:59:59 自动取消订单</div>-->
          </div>
        </div>
      </van-sticky>

      <div class="m-10 p-12 rounded-10 bg-white">
        <OGoods :goods="orderInfo" :orderNo="orderInfo.no" afterSales />
      </div>

      <div class="m-10 px-10 py-20 rounded-10 bg-white lh-14">
        <div class="flex items-center justify-between mb-20">
          <div class="text-black">商品总额</div>
          <div class="flex items-center">
            <div class="text-[#989898] mr-5">商品总价</div>
            <div class="ml-5 text-black font-500">¥{{ formatMoney(orderInfo?.orderTotalPrice) }}</div>
          </div>
        </div>
        <div class="flex items-center justify-between mb-20">
          <div class="text-black">运费</div>
          <div class="flex items-center">
            <div class="text-[#989898] mr-5">运费（快递）</div>
            <div class="ml-5 text-black font-500">¥{{ formatMoney(orderInfo?.orderPrice?.orderTotalFreight || 0) }}</div>
          </div>
        </div>
        <div class="flex items-center justify-between mb-20">
          <div class="text-black">合计</div>
          <div class="font-500 text-black">¥{{ formatMoney(orderInfo?.orderTotalPrice || 0) }}</div>
        </div>
        <div class="flex items-center justify-between mb-20" v-if="orderInfo.payScore">
          <div class="text-black">{{ orderInfo.scoreType == 0 ? '福利积分' : '扶贫积分' }}</div>
          <div class="font-500 text-black">¥{{ formatMoney(orderInfo.payScore || 0) }}</div>
        </div>
        <div class="flex items-center justify-between" v-if="orderInfo.payPrice">
          <div class="text-black">现金支付</div>
          <div class="color-primary font-600 text-17">¥{{ formatMoney(orderInfo.payPrice || 0) }}</div>
        </div>
      </div>

      <div class="m-10 px-10 py-20 rounded-10 bg-white lh-14" v-if="orderInfo.address.name">
        <div class="text-black font-600 lh-14">收货信息</div>
        <div class="text-[#6C6C6C] lh-18 mt-15">
          {{ orderInfo.address.name }}，{{ orderInfo.address.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') }}，{{
            getAddress(orderInfo.address)
          }}
        </div>
      </div>

      <div class="m-10 px-10 py-20 rounded-10 bg-white lh-14">
        <div class="flex items-center justify-between">
          <div>订单编号</div>
          <div class="flex items-center">
            <div class="text-[#989898]">{{ orderInfo.no }}</div>
            <div class="w-1 h-14 bg-[#EAE9E9] ml-10 mr-12"></div>
            <div class="text-[#000000] font-600" @click="handleCopy">复制</div>
          </div>
        </div>
        <div class="flex items-center justify-between mt-20">
          <div>下单时间</div>
          <div class="text-[#989898]">{{ formatDateTime(orderInfo.orderSubmitTime) }}</div>
        </div>
        <div class="flex items-center justify-between mt-20" v-if="orderInfo.orderPayTime">
          <div>付款时间</div>
          <div class="text-[#989898]">{{ formatDateTime(orderInfo.orderPayTime) }}</div>
        </div>
        <div class="flex items-center justify-between mt-20" v-if="orderInfo.orderDeliveryTime">
          <div>发货时间</div>
          <div class="text-[#989898]">{{ formatDateTime(orderInfo.orderDeliveryTime) }}</div>
        </div>
        <div class="flex items-center justify-between mt-20" v-if="orderInfo.orderFinishTime">
          <div>完成时间</div>
          <div class="text-[#989898]">{{ formatDateTime(orderInfo.orderFinishTime) }}</div>
        </div>
      </div>
    </div>

    <div class="position-fixed left-0 bottom-0 bg-white flex items-center justify-end w-full lh-16 bottom van-safe-area-bottom" v-if="orderInfo.status !== 9">
      <div class="p-15">
        <van-button round plain class="!ml-10 !h-29 !px-10 !text-[#6C6C6C]" to="/address/index?type=submit-order" v-if="false">修改地址</van-button>
        <van-button round plain class="!mr-10 !h-29 !px-10 !text-[#6C6C6C]" @click="handleCancle" v-if="orderInfo.status === 1">取消订单</van-button>
        <van-button round plain class="!ml-10 !h-30 !px-10" color="#999999" :to="`/order/logistics?orderNo=${orderInfo.no}`" v-if="[2,3,4,5,8].includes(orderInfo.status)">查看物流</van-button>
        <van-button round plain class="!ml-10 !h-29 !px-10 btn-primary" v-if="orderInfo.status === 1 && !orderInfo.payed" @click.stop="handlePayOrder">立即付款</van-button>
  <!--      <van-button round plain class="!ml-10 !h-29 !px-10 btn-primary" @click="applyAfterSale" v-if="[2,3,4,5,8].includes(orderInfo.status)">申请售后</van-button>-->
      </div>
    </div>

    <cancel ref="cancelRef" @submit="submitCancel"></cancel>
  </div>
</template>

<script setup lang="ts">
import { useClipboard } from '@vueuse/core'

defineOptions({
  name: 'orderDetail'
})
import { closeToast, showLoadingToast, showToast } from 'vant'
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { AppAddressRespVO } from '@/api/address'
import {orderCancel, orderDetail} from '@/api/order'
import money from '@/assets/order/money.png'
import NavBar from '@/components/navbar/index.vue'
import { useUserStore } from '@/stores/user'
import { formatMoney } from '@/utils/base'
import { formatDateTime, getStatusLabel } from "@/utils/orderUtils"
import cancel from '@/views/order/components/cancel.vue'
import OGoods from '@/views/order/components/oGoods.vue'

const { copy } = useClipboard()

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const addressInfo = ref(userStore.defaultAddress)
const cancelRef = ref()
const orderInfo = ref({
  orderPrice: {},
  address: {}
})

const handlePayOrder = () => {
  router.push({
    path: '/order/pay',
    query: {
      no: orderInfo.value.no
    }
  })
}

const handleCopy = async () => {
  await copy(orderInfo.value.no)
  showToast('复制成功')
}

const getAddress = (item: AppAddressRespVO) =>
  `${ item.provinceName }${ item.cityName }${ item.countyName }${ item.townName || '' }${ item.consigneeAddress || '' }`

const getOrderDetail = () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0
  })
  orderDetail({
    orderNo: route.query.no
  })
    .then((res) => {
      res.items = res.skuInfoList || []
      orderInfo.value.address = addressInfo
      orderInfo.value = res
    })
    .finally(() => {
      closeToast()
    })
}

const handleCancle = () => {
  cancelRef.value.show = true
}

const submitCancel = async (cancelReason: string) => {
  await orderCancel({ orderNo: orderInfo.value.no, cancelReason })
  getOrderDetail()
}

onMounted(() => {
  getOrderDetail()
})
</script>

<style scoped lang="scss">
.payType {
  background: url('@/assets/order/group.png') no-repeat center;
  background-size: 100% 100%;
}

.bottom {
  box-shadow: 0px 2px 20px 0px rgba(174, 174, 174, 0.5);
}
</style>
