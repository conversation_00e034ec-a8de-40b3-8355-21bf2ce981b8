<script setup lang="ts">
defineOptions({
  name: 'VRadio'
})

import checked from '@/assets/shoppingCart/checked.png'
import unchecked from '@/assets/shoppingCart/unchecked.png'

const props = defineProps({
  size: {
    type: [String, Number],
    default: '18'
  }
})
</script>

<template>
  <van-radio v-bind="$attrs">
    <slot />
    <template #icon="props">
      <img :style="{width: `${size}px`, height: `${size}px`}" :src="props.checked ? checked : unchecked" />
    </template>
  </van-radio>
</template>

<style scoped lang="scss"></style>
