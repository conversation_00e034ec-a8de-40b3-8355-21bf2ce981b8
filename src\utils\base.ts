import dayjs from 'dayjs'

/**
 * Formats a number as a money value with two decimal places and comma separators for thousands.
 *
 * @param {number} value - The number to be formatted as money.
 * @return {string} The money value as a string with two decimal places and comma separators.
 */
export function formatMoney(value: number) {
  if (!value) {
    return '0.00'
  }
  return Number(value)
    .toFixed(2)
    .replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')
}

export function getDate(date: any, format: any = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) {
    return ''
  }
  return dayjs(date).format(format)
}
