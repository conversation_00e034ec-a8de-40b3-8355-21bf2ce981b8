<template>
    <div class="flex items-center justify-between mb-2 px-2">
        <div class="flex items-center">
            <van-icon v-show="comment.avatar != null" :name="comment.avatar" class="mr-2" />
            <span class="text-gray-500 text-12 mr-10">{{ formatName(comment.nickName) }}</span>
            <van-rate v-model="comment.score" size="14px" readonly class="text-red-500" />
        </div>
        <div class="text-gray-400 text-12">{{ formatDate(comment.createTime) }}</div>
    </div>
    <div class="text-gray-600 mb-2 text-12 px-2 pt-10">
        {{ comment.content }}
    </div>
    <div class="flex space-x-10 mb-2 pt-10">
        <van-image v-for="(img, imgIndex) in comment.pics ? comment.pics.split(',') : []" :key="imgIndex" :src="img"
            width="60" height="60" @click="previewImg(imgIndex)"/>
        <van-image-preview v-model:show="showPreview" :images="comment.pics ? comment.pics.split(',') : []"
            :startPosition="currentImageIndex" :closeable="true" />
    </div>
</template>

<script setup lang="ts">
import { showToast, ImagePreview } from 'vant'
import { Comment, computed, nextTick, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { getProductCommentPage, like } from '@/api/comment'
import NavBar from '@/components/navbar/index.vue'
import { formatDate } from '@/utils/orderUtils'

defineOptions({
    name: 'Comment'
})

const props = defineProps({
    comment: {
        type: Object,
        default: () => { }
    },
})

let showPreview = ref<boolean>(false)
let currentImageIndex = ref<number>(0)

const route = useRoute()
const commentList = ref<any>([])

const formatName = (nickName) => {
    if (nickName != null && nickName.length > 2) {
        const startChar = nickName[0]; // 获取第一个字符
        const endChar = nickName[nickName.length - 1]; // 获取最后一个字符
        const middleStars = '*'.repeat(nickName.length - 2); // 中间部分用星号替换
        return `${startChar}${middleStars}${endChar}`;
    } else if (nickName != null && nickName.length === 2) {
        return nickName.substring(0, 1) + '*';
    } else {
        return "匿名";
    }
}

const previewImg = (imgIndex: number) => {
    currentImageIndex.value = imgIndex
    showPreview.value = true
}

</script>


<style scoped lang="scss"></style>