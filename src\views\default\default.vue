<template>
  <div class="default-wrap">
    <img :src="defaultImg" class="size-66 loading-animated" />
    <div class="text-14 color-#6C6C6C mt-20 mb-20">系统正维护或重启，请等待~</div>
    <div class="retry-btn" @click="toRetry">重试</div>
  </div>
</template>

<script setup lang="ts">
import { useRoute,useRouter } from 'vue-router'

import defaultImg from '@/assets/default.png'

const router = useRouter()
const route = useRoute()
const toRetry = () => {
  router.push('/')
}

const autoRetry = () => {
  toRetry()
  if(route.path === '/default') {
    setTimeout(toRetry, 30 * 1000)
  }
}

setTimeout(autoRetry, 30 * 1000)
</script>

<style scoped lang="scss">
.default-wrap {
  display: flex;
  margin-top: 200px;
  align-items: center;
  flex-direction: column
}
.retry-btn {
  width: 88px;
  height: 34px;
  line-height: 34px;
  border-radius: 51px;
  border: 1px solid $primary-color;
  color: $primary-color;
  text-align: center;
}
.loading-animated {
  animation: spin 2s linear infinite;
}
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

</style>
