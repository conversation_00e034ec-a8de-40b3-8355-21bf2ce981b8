<template>
  <div class="pb-120">
    <van-sticky>
      <div class="header flex-center bg-white py-14">
        <div class="text-black font-600 text-17">购物车</div>
        <div class="flex items-center right text-black" v-if="info?.shopItems?.length">
          <!--          <img :src="search" class="w-20 h-20" />-->
          <span class="ml-10" @click="handleManage">{{ !manager ? '管理' : '完成' }}</span>
        </div>
      </div>
    </van-sticky>

    <template v-if="info?.shopItems?.length">
      <div
        class="card rounded-10 bg-white pb-15 pl-8 pr-10 mt-10 mx-10"
        v-for="(item, index) in info?.shopItems"
        :key="index"
      >
        <div class="flex items-center py-11">
          <van-checkbox @click.stop :disabled="manager ? !manager : item.disabled">
            <span class="text-black font-600 text-14">{{ item.supplierName }}</span>
            <template #icon="props" v-if="!item.disabled || manager">
              <img :src="getSrc(item.selected as boolean)" class="size-18" @click="shopNameClick(item)" />
            </template>
          </van-checkbox>
        </div>
        <ul>
          <li class="flex items-center py-15" v-for="(child, idx) in item.skuItems" :key="idx" @click="toDetail(child)">
            <van-checkbox @click.stop style="flex-shrink: 0" :disabled="manager ? !manager : child.disabled">
              <template #icon="props" v-if="!child.disabled || manager">
                <img :src="getSrc(child.selected as boolean)" class="size-18" @click.stop="skuClick(child, item)" />
              </template>
            </van-checkbox>
            <div class="size-94 position-relative">
              <img :src="child.picUrl" class="size-94 rounded-5 ml-8" />
              <img v-if="isOutStock(child.stockStateType)" :src="outStock" class="out-stock size-72">
            </div>
<!--            <img :src="child.picUrl" class="size-94 rounded-5 ml-8" />-->
            <div class="ml-10 flex flex-col justify-between w-100%">
              <div class="font-600 text-14 text-black ellipsis-2">
                {{ child.skuName }}
              </div>
              <div class="mt-8 mb-12">
                <!--                <span class="spec px-8 py-4" @click="specClick(child, idx)">{{ child.desc }}</span>-->
              </div>
              <div class="flex items-center justify-between">
                <span class="amount"><span class="unit">¥</span>{{ saveTwoDecimal(child.skuPrice) }}</span>
                <van-stepper
                  v-model="child.count"
                  input-width="27px"
                  :min="child.lowestBuy || 1"
                  :max="99999"
                  @plus="stepperChange(child)"
                  @minus="stepperChange(child)"
                  @click.stop
                />
              </div>
            </div>
          </li>
        </ul>
      </div>

      <div class="bottom flex items-center justify-between pl-17 pr-10">
        <div class="flex items-center pb-15 pt-15">
          <van-checkbox @click.stop :disabled="allDisabled">
            <span>全选</span>
            <template #icon="props" v-if="!allDisabled">
              <img :src="getSrc(allChecked as boolean)" class="size-18" @click="allCheckedClick" />
            </template>
          </van-checkbox>
        </div>
        <div class="flex items-center pb-15 pt-15" v-if="!manager">
          <span class="mr-7 font-600 text-black text-14">合计所需积分</span>
          <div class="flex items-center amount mr-11">
            <span class="unit">¥</span>
            <span>{{ saveTwoDecimal(info.payPrice) }}</span>
          </div>
          <van-button round size="small" class="jiesuan ml-10 btn-primary" @click="toOrderConfirm"
            >去结算({{ info.selectedCount }})</van-button
          >
        </div>
        <div class="flex items-center pb-15 pt-15" v-else>
          <van-button plain round size="small" class="ml-10 w-77 btn-primary" @click="deleteClick">删除</van-button>
        </div>
      </div>
    </template>
    <template v-else>
      <EmptyBox />
    </template>

    <van-action-sheet v-model:show="show" title="">
      <div class="content position-relative">
        <img :src="close" class="size-18 position-absolute top-10 right-10" @click="show = false" />
        <div class="flex items-end p-15">
          <img :src="oil" class="size-58 rounded-5" />
          <div class="ml-18 flex flex-col">
            <div class="flex items-center amount mb-11">
              <span class="unit">¥</span>
              <span class="text-12"><span class="text-18">134</span>.24</span>
            </div>
            <div class="flex items-center">
              <span class="mr-4 text-[#6C6C6C] text-14">已选</span>
              <span class="text-black text-14">黑胡椒味400g</span>
            </div>
          </div>
        </div>

        <div style="max-height: 350px; overflow: auto">
          <div class="text-14 text-black mt-18 mb-17 font-600 pl-15">选择</div>

          <div class="specList pl-10 pr-15">
            <div class="specItem active">黑胡椒黑胡椒味400g</div>
            <div class="specItem">黑胡</div>
            <div class="specItem">黑胡椒</div>
            <div class="specItem">黑胡椒</div>
            <div class="specItem">黑胡椒味400g</div>
            <div class="specItem">黑胡椒味400g</div>
            <div class="specItem">黑胡椒味400g</div>
          </div>

          <div class="mt-17 flex justify-between pl-15 pr-22 mb-38">
            <div class="flex items-center">
              <div class="mr-9 font-600 text-black">库存数量</div>
              <div class="text-[#6C6C6C]">剩余234件</div>
            </div>
            <van-stepper v-model="buyNumber" />
          </div>
        </div>

        <div class="ml-15 mr-15 mb-29">
          <van-button class="btn-primary" block round>完成</van-button>
        </div>
      </div>
    </van-action-sheet>
  </div>
</template>

<script setup lang="ts">
import { closeToast, showLoadingToast, showToast } from 'vant'
import { nextTick, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

import { addressDefault, AppAddressRespVO } from '@/api/address'
import type { CartDetailRespVO, CartShopItem, CartSkuItem } from '@/api/shoppingCart'
import { cartDelete, cartUpdateCount, cartUpdateSelected, getShoppingCartList, checkMix } from '@/api/shoppingCart'
import outStock from '@/assets/goods/outStock.png'
import oil from '@/assets/home/<USER>'
import checked from '@/assets/shoppingCart/checked.png'
import close from '@/assets/shoppingCart/close.png'
import search from '@/assets/shoppingCart/search.png'
import unchecked from '@/assets/shoppingCart/unchecked.png'
import EmptyBox from '@/components/emptyBox/index.vue'
import { useUserStore } from '@/stores/user'
import {isOutStock} from '@/utils/productUtils'
import { debug } from 'console'

const userStore = useUserStore()
const allChecked = ref(false)
const allDisabled = ref(false)
const manager = ref(false)
const show = ref(false)
const buyNumber = ref(1)
const info = ref<CartDetailRespVO>({
  count: 0,
  payPrice: 0,
  selectedCount: 0,
  shopItems: []
})

const handleManage = () => {
  manager.value = !manager.value
  info.value.shopItems?.forEach((item) => {
    if (!manager.value) {
      item.skuItems?.forEach((child: any) => {
        if (child.disabled) {
          child.selected = false
        }
      })
      const flag = item.skuItems?.filter((p) => !p.disabled)?.every((child: any) => child.selected)
      item.selected = flag
    } else {
      const flag = item.skuItems?.every((child: any) => child.selected)
      item.selected = flag
    }
  })
  allChecked.value = info.value.shopItems?.every((item) => item.selected) as boolean
}

const getSrc = (haschecked: boolean): string => (haschecked ? checked : unchecked)

const specClick = (item: any, idx: number) => {
  show.value = true
  console.log(item, idx)
}

const router = useRouter()
const toOrderConfirm = () => {
  if (!info.value.selectedCount) {
    return showToast('请选择商品')
  }
  checkCartMix().then(res => {
    if (res) {
      router.push('/order/orderConfirm')
    }
  })
}

// 获取shopItems里面的skuItems里面的skuId
const getSkuIds = () => {
  const skuIds: string[] = []
  info.value?.shopItems?.forEach((item) => {
    item.skuItems?.forEach((child) => {
      skuIds.push(child.skuId as string)
    })
  })
  return skuIds
}

// 保留两位小数
const saveTwoDecimal = (val: number | string = 0) => (Math.round(val * 100) / 100).toFixed(2)

const getSelectCount = () => {
  let selectedCount = 0
  let totalPrice = 0
  info.value?.shopItems?.forEach((item) => {
    !item.disabled &&
      item.skuItems?.forEach((child) => {
        if (child.selected && !child.disabled) {
          selectedCount += child.count as number
          totalPrice += ((child.skuPrice as string) * (child.count as number)) as number
        }
      })
  })
  info.value.selectedCount = selectedCount
  info.value.payPrice = totalPrice
}

const getCartCountFn = (type: string, item: CartShopItem) => {
  let selected = false
  let skuIds: (string | undefined)[] | undefined = []
  if (type === 'shopName') {
    selected = item.selected
    skuIds = item.skuItems?.map((child) => child.skuId)
  } else if (type === 'sku') {
    selected = item.selected
    skuIds = [item.skuId]
  } else if (type === 'all') {
    selected = allChecked.value
    skuIds = getSkuIds()
  }
  if (!manager.value || (manager.value && !item.disabled)) {
    cartUpdateSelected({
      selected: selected,
      skuIds: skuIds
    })
    userStore.loadCartCount()
    getSelectCount()
  }
}

const hasAllChecked = () => {
  let flag = true
  info.value.shopItems?.forEach((item) => {
    if (!item.selected && !item.disabled) {
      flag = false
    }
  })
  allChecked.value = flag
  allDisabled.value = info.value.shopItems?.every((item) => item.disabled) as boolean
}

const shopNameClick = (item: any) => {
  if (item.disabled && !manager.value) {
    return
  }
  item.selected = !item.selected
  item.skuItems.forEach((child: CartSkuItem) => {
    if (!manager.value) {
      child.selected = item.selected && !child.disabled
    } else {
      child.selected = item.selected
    }
  })
  hasAllChecked()
  getCartCountFn('shopName', item)
}

const skuClick = (item: CartSkuItem, parent: CartShopItem) => {
  if (item.disabled && !manager.value) {
    return
  }
  item.selected = !item.selected
  if (!manager.value) {
    const flag = parent.skuItems?.filter((p) => !p.disabled)?.every((child: any) => child.selected)
    parent.selected = flag
  } else {
    const flag = parent.skuItems?.every((child: any) => child.selected)
    parent.selected = flag
  }

  hasAllChecked()
  getCartCountFn('sku', item)
}

const allCheckedClick = () => {
  allChecked.value = !allChecked.value
  info.value.shopItems?.forEach((item) => {
    if (!item.disabled && !manager.value) {
      item.selected = allChecked.value
      item.skuItems?.forEach((child) => {
        child.selected = !child.disabled && allChecked.value
      })
    } else {
      item.selected = allChecked.value
      item.skuItems?.forEach((child) => {
        child.selected = allChecked.value
      })
    }
  })
  getCartCountFn('all', {})
}

const stepperChange = async (child: CartSkuItem) => {
  await nextTick()
  cartUpdateCount({
    skuId: child.skuId,
    count: child.count,
    area: {
      provinceId: 17,
      cityId: 1381,
      countyId: 50712,
      townId: 62960
    }
  })
  userStore.loadCartCount()
  getSelectCount()
}

const defaultAddress = ref<AppAddressRespVO>({})
const getaddressDefault = async () => {
  const res = await addressDefault()
  defaultAddress.value = res
}

const getList = async () => {
  await getaddressDefault()
  showLoadingToast({
    duration: 0,
    message: '加载中...',
    forbidClick: true
  })
  getShoppingCartList({
    provinceId: defaultAddress.value.provinceId,
    cityId: defaultAddress.value.cityId,
    countyId: defaultAddress.value.countyId,
    townId: defaultAddress.value.townId
  })
    .then((res) => {
      info.value = res || []
      info.value.shopItems?.forEach((item) => {
        item?.skuItems.forEach((child) => {
          child.disabled = child.skuPrice === null
        })
        const count = item.skuItems?.filter((p) => !p.disabled)?.reduce((prev, curr) => prev + curr.count, 0)
        item.selected = item.selectedCount === count
        item.disabled = item?.skuItems?.every((child) => child.disabled)
      })
      hasAllChecked()
    })
    .finally(() => {
      closeToast()
    })
}

const deleteClick = () => {
  const skuIds: string[] = []
  info.value.shopItems?.forEach((item) => {
    item.skuItems?.forEach((child) => {
      if (child.selected) {
        skuIds.push(child.skuId as string)
      }
    })
  })
  if(!skuIds.length) {
    return showToast('请选择商品')
  }
  cartDelete(skuIds?.join(',')).then(() => {
    getList()
    userStore.loadCartCount()
  })
}

const checkCartMix = () => {
  return checkMix().then(res => {
    if (!res) {
      showToast('福利商品和扶贫商品不能混合结算')
      return false
    }
    return true
  })
}

const toDetail = (skuItem: CartSkuItem) => {
  router.push({
    path: '/goods/detail',
    query: {
      skuId: skuItem.skuId,
      supplierId: skuItem.supplierId
    }
  })
}

onMounted(() => {
  getList()
  userStore.loadCartCount()
})
</script>

<style scoped lang="scss">
.header {
  position: relative;
  .right {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
  }
}

.amount {
  color: $primary-color;
  font-size: 17px;
  font-weight: 600;
  .unit {
    font-size: 12px;
  }
}

.spec {
  font-size: 12px;
  color: #6c6c6c;
  line-height: 12px;
  background: #f2f2f2;
  border-radius: 10px;
}

:deep(.van-stepper) {
  .van-stepper__input {
    width: 27px;
    height: 20px;
    margin-left: 10px;
    margin-right: 10px;
    border-radius: 2px;
  }
  .van-stepper__minus,
  .van-stepper__plus {
    width: 16px;
    height: 16px;
    background: none;
    color: #000000;
  }
  .van-stepper__minus--disabled,
  .van-stepper__plus--disabled {
    color: #d9d9d9;
  }
}
$env-bottom-h: env(safe-area-inset-bottom);
$van-tabbar-h: var(--van-tabbar-height);
.bottom {
  position: fixed;
  bottom: calc($van-tabbar-h + $env-bottom-h);
  left: 0;
  right: 0;
  height: 58px;
  background-color: #fff;
  :deep(.van-button--default.jiesuan) {
    font-size: 14px;
    font-weight: 600;
    width: 88px;
  }
}

.specList {
  display: flex;
  flex-wrap: wrap;
  .specItem {
    background: #f2f2f2;
    border-radius: 20px;
    padding: 10px;
    color: #6c6c6c;
    line-height: 14px;
    border: 1px solid #f2f2f2;
    margin-left: 5px;
    margin-bottom: 8px;
    flex: 0 0 auto;
    &.active {
      background: #fff;
      color: $primary-color;
      border: 1px solid $primary-color;
    }
  }
}
.out-stock {
  position: absolute;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
}
</style>
