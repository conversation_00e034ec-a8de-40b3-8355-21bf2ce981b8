import { getRefreshToken } from '@/utils/auth'
import request from '@/utils/request'

type dataType = {
  mobile: string,
  password: string
}

const requestM = (data: any) => request({
  ...data,
  ...{
    baseURL: import.meta.env.VITE_BASE_MEMBER_API
  }
})

// 使用手机 + 密码登录
export function loginUser(data: dataType): any {
  return requestM({
    url: '/auth/login',
    method: 'post',
    data
  })
}

// 使用手机 + 短信验证码
export function loginSmsCode(data: any): any {
  return requestM({
    url: '/auth/sms-login',
    method: 'post',
    data
  })
}

// 发送短信验证码
export function sendSmsCode(data: any) {
  return requestM({
    url: '/auth/send-sms-code',
    method: 'post',
    data
  })
}

// 重置密码
export function resetPassword(data: any) {
  return requestM({
    url: '/auth/reset-password',
    method: 'post',
    data
  })
}

// sso登录
export function ssoLogin(data: any) {
  return requestM({
    url: '/auth/sso-login',
    method: 'post',
    data
  })
}

// 获取退出登录信息
export function getSsoLogoutInfo(params: any) {
  return requestM({
    url: '/auth/sso-logout',
    method: 'get',
    params
  })
}

// 获取退出登录信息
export function getSsoLogoutInfoV2(params: any) {
  return requestM({
    url: '/auth/sso-logout-v2',
    method: 'get',
    params
  })
}

// 刷新访问令牌
export function refreshToken() {
  return requestM({
    url: `/auth/refresh-token?refreshToken=${ getRefreshToken() }`,
    method: 'post'
  })
}