import type { AxiosResponse,InternalAxiosRequestConfig } from 'axios'
import axios from 'axios'
import { showToast } from 'vant'

import { refreshToken } from '@/api/login'
import router from '@/router/index'
import { useHomeStore } from '@/stores/home'
import { useUserStore } from '@/stores/user'
import { toLogin } from '@/utils/ssoUtils'

import { getRefreshToken, getToken, setToken } from './auth'

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_BASE_API,
  timeout: 50000,
  headers: { 'Content-Type': 'application/json;charset=utf-8' }
})

// Axios 无感知刷新令牌，参考 https://www.dashingdog.cn/article/11 与 https://segmentfault.com/a/1190000020210980 实现
// 请求队列
let requestList: any[] = []
// 是否正在刷新中
let isRefreshToken = false

function handleAuthorized() {
  showToast('登录已过期，请重新登录')
  setTimeout(() => {
    toLogin(1)
  }, 1000)
  return Promise.reject('无效的会话，或者会话已过期，请重新登录。')
}

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const homeStore = useHomeStore()
    if (homeStore.configInfo.configData.tenantId) {
      config.headers['tenant-id'] = homeStore.configInfo.configData.tenantId
      if(getToken()) {
        config.headers.Authorization = `Bearer ${ getToken() }`
      }
    }
    return config
  },
  (error: any) => Promise.reject(error)
)

// 响应拦截器
service.interceptors.response.use(
  async (response: AxiosResponse) => {
    const { code, msg, data } = response.data
    if (+code === 0) {
      return data
    } else if (+code === 401) {
      if (!isRefreshToken) {
        isRefreshToken = true;
        // 1. 如果获取不到刷新令牌，则只能执行登出操作
        if (!getRefreshToken()) {
          return handleAuthorized();
        }
        // 2. 进行刷新访问令牌
        try {
          const userStore = useUserStore()
          const refreshTokenRes = await refreshToken()
          // 2.1 刷新成功，则回放队列的请求 + 当前请求
          setToken(refreshTokenRes)
          requestList.forEach(cb => cb())
          userStore.loadCartCount()
          return service(response.config)
        } catch (e) {// 为什么需要 catch 异常呢？刷新失败时，请求因为 Promise.reject 触发异常。
          // 2.2 刷新失败，只回放队列的请求
          requestList.forEach(cb => cb())
          // 提示是否要登出。即不回放当前请求！不然会形成递归
          return handleAuthorized();
        } finally {
          requestList = []
          isRefreshToken = false
        }
      } else {
        // 添加到队列，等待刷新获取到新的令牌
        return new Promise(resolve => {
          requestList.push(() => {
            response.config.headers.Authorization = `Bearer ${  getToken() }` // 让每个请求携带自定义token 请根据实际情况自行修改
            resolve(service(response.config))
          })
        })
      }
    } else if (+code === 503) {
      router.push({path: '/default'})
      return Promise.reject(new Error(msg || 'Error'))
    }
    // 响应数据为二进制流处理(Excel导出)
    if (response.data instanceof ArrayBuffer || response.data instanceof Blob) {
      return response
    }

    showToast(msg || '系统出错')
    return Promise.reject(new Error(msg || 'Error'))
  },
  (err: any) => {
    if (err.response.data) {
      const { code, error } = err.response.data
      showToast(error || '系统出错')
    }
    return Promise.reject(err.message)
  }
)

// 导出 axios 实例
export default service
