import request from '@/utils/request'

const requestO = (data: any) =>
  request({
    ...data,
    ...{
      baseURL: import.meta.env.VITE_BASE_TRADE_API
    }
  })

// 申请售后
export function afterSaleCreate(data: any) {
  return requestO({
    url: '/after-sale/create',
    method: 'post',
    data
  })
}

// 查询售后单详情
export function afterSaleDetail(params: any) {
  return requestO({
    url: '/after-sale/detail',
    method: 'get',
    params
  })
}

// 取消售后
export function afterSaleCancel(params: any) {
  return requestO({
    url: '/after-sale/cancel',
    method: 'delete',
    params
  })
}

// 查看售后详情
export function afterOrderItemSaleDetail(params: any) {
  return requestO({
    url: '/after-sale/order-item/detail',
    method: 'get',
    params
  })
}

// 提交物流信息
export function saveDelivery(data: any) {
  return requestO({
    url: "/after-sale/delivery",
    method: "post",
    data,
  });
}