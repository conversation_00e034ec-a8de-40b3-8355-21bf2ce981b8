/* eslint-env node */
require('@rushstack/eslint-patch/modern-module-resolution')
const OFF = 'off'
const ERROR = 'error'
const WARN = 'warn'
module.exports = {
  root: true,
  extends: [
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier/skip-formatting'
  ],
  parserOptions: {
    ecmaVersion: 'latest'
  },
  plugins: ['vue', '@typescript-eslint', 'simple-import-sort'],
  rules: {
    'vue/multi-word-component-names': OFF,
    'generator-star-spacing': OFF,
    'space-before-function-paren': OFF, //函数前边不允许有空格并修复'
    'array-bracket-spacing': [ERROR, 'never'], //数组内容和内容1个空格，边界不需要空格['1', '2']并修复；
    'array-element-newline': [ERROR, 'consistent'], //数字内容换行的时候报错并修复；
    'array-callback-return': ERROR, //数组的form/every/filter/find/findIndex/reduce/reduceRight/some/sort没有return的时候报错；
    'block-scoped-var': ERROR, //变量在定义块的外部使用时，规则会生成警告
    'complexity': [ERROR, 30], //循环复杂度测量程序源代码中线性独立路径的数量
    'for-direction': ERROR, //无限循环代码报错
    'func-call-spacing': [ERROR, 'never'], //调用的方法前边不允许有空格并修复
    'func-name-matching': [ERROR, 'never'], //函数名称与它们所分配的变量或属性的名称相匹配
    'eqeqeq': [ERROR, 'smart'], //运算规则符号===/!==校验
    'lines-between-class-members': [ERROR, 'always'], //同一个class方法中间空行
    'max-depth': [ERROR, 4], //最深的层级，其他可以另写方法
    'max-lines': [ERROR, 5000], //单个文件的最大500行 (先改成5000吧， 防止有些代码提交不上去。。。。。)
    'max-params': [ERROR, 4], //单个方法的入参数4个限制
    'max-statements-per-line': [ERROR, { max: 2 }], //每行允许有几个方法
    'no-bitwise': ERROR, //运算符检查'||',error'|'
    'no-else-return': ERROR, //if有return else不需要使用并且自动修复
    'no-empty': ERROR, //方法块里边没有执行其他语句
    'no-empty-function': ERROR, //空方法报错
    'no-lonely-if': ERROR, //如果一个if陈述是该else块中唯一的陈述，那么使用一个else if表格通常会更清晰
    'no-mixed-spaces-and-tabs': ERROR, //不允许使用混合空格和制表符进行缩进
    'no-multiple-empty-lines': [ERROR, { max: 1, maxEOF: 1 }], //允许文件结尾处强制执行最大数量的连续空1行并且自动修复
    'no-nested-ternary': ERROR, //规则不允许嵌套的三元表达式
    'no-template-curly-in-string': ERROR, //它会在发现一个包含模板文字 place holder（${something}）的字符串时发出警告
    'no-trailing-spaces': ERROR, //不允许在行尾添加尾随空白git对比差异产生冲突，自动修复
    'no-unreachable': ERROR, //不允许可达代码后return，throw，continue，和break语句
    'no-useless-concat': ERROR, //标记2个文字的连接，当它们可以合并成一个文字时
    'no-var': ERROR, //阻止var使用或鼓励改为使用const或let,并且自动修复
    'require-await': ERROR, //警告不具有await表达式的异步函数
    'semi-spacing': [ERROR, { before: false, after: true }], //防止在表达式中使用分号之前的空格。
    'space-before-blocks': [ERROR, 'always'], //blocks块必须至少有一个先前的空间
    'spaced-comment': [ERROR, 'always', { block: { balanced: true } }], //强制间距的一致性//或/*
    'no-duplicate-imports': [ERROR, { includeExports: true }],
    'template-curly-spacing': [ERROR, 'always'],
    'no-self-compare': WARN,
    'no-unmodified-loop-condition': WARN,
    'no-use-before-define': [ERROR, 'nofunc'],
    'default-case': ERROR,
    'curly': ERROR,
    'default-param-last': ERROR,
    'no-caller': WARN,
    'no-invalid-this': WARN,
    'no-return-assign': [ERROR, 'always'],
    'no-shadow': ERROR,
    'no-unneeded-ternary': WARN,
    'no-useless-return': WARN,
    'prefer-const': WARN,
    'prefer-object-spread': WARN,
    'dot-notation': ERROR,
    'no-eval': ERROR,
    'prefer-template': WARN,
    'no-new-func': ERROR,
    'no-param-reassign': WARN,
    'arrow-body-style': [ERROR, 'as-needed'],
    'indent': [ERROR, 2, { 'SwitchCase': 1 }],
    "class-methods-use-this": "off", // 因为AxiosCancel必须实例化而能静态化所以加的规则，如果有办法解决可以取消
    "simple-import-sort/exports": ERROR,
    'simple-import-sort/imports': ERROR
  }
}
