<template>
  <div class="login-container">
    <div class="login-bg"></div>
    <div class="login">
      <img :src="logo" class="size-110 logo" />
      <div class="text-16 font-600 mb-30" style="color: #3c3b3b;text-align:center;">欢迎来到工会会员福利平台</div>

      <van-tabs v-model:active="curLoginMethod">
        <van-tab :title="item.title" :name="item.code" v-for="item in loginMethods" :key="item.code"></van-tab>
      </van-tabs>

      <div class="mt-20">
        <div class="mt-10 flex">
          <img :src="phone" class="size-20 mr-5" />
          <div class="color-#333 font-600">手机号：</div>
        </div>
        <van-field class="formInput" v-model.trim="form.userName" placeholder="请输入手机号" :border="false">
          <template #button v-if="curLoginMethod === '20'">
            <van-button size="small" :disabled="sendDisabled" @click="sendCode" class="!text-black !text-12 !font-400 sendCode">{{ sendText }}</van-button>
          </template>
        </van-field>
        <template v-if="curLoginMethod === '20'">
          <div class="mt-10 flex" >
            <img :src="smcode" class="size-20 mr-5" />
            <div class="color-#333 font-600">验证码：</div>
          </div>
          <van-field class="formInput" v-model.trim="form.verifyCode" :maxlength="6" placeholder="请输入验证码" :border="false"></van-field>
        </template>
        <template v-if="curLoginMethod === '10'">
          <div class="mt-10 flex">
            <img :src="password" class="size-20 mr-5" />
            <div class="color-#333 font-600">密码：</div>
          </div>
          <van-field class="formInput mt-20 mb-20" v-model="form.password" :type="passwordType" placeholder="请输入密码" :border="false" :maxlength="50">
            <template #right-icon>
              <van-icon :name="passwordType === 'password' ? 'closed-eye' : 'eye-o'"
                @click="passwordType = passwordType === 'password' ? 'text' : 'password'" />
            </template>
          </van-field>
        </template>

        <div class="flex gap-25 mt-20">
          <van-button color="#D9D9D9" size="small" block type="default" @click="backLogin">返回</van-button>
          <van-button class="btn-primary" size="small" block type="default" :disabled="loginBtnDisabled" @click="doLogin">登录</van-button>
        </div>
        <div class="font-600 mt-20">
          首次登录或忘记密码请<span class="color-primary" @click="toFindPass">点击设置</span>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { kMaxLength } from 'buffer'
import { showToast } from 'vant'
import { computed, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { sendSmsCode } from '@/api/login'
import logo from '@/assets/login/logo.png'
import password from '@/assets/login/password.png'
import phone from '@/assets/login/phone.png'
import smcode from '@/assets/login/smcode.png'
import { useUserStore } from '@/stores/user'
import { encryptSm2 } from '@/utils/auth'

const props = defineProps({
  sourceTag: {
    type: String
  }
})

const loginMethods = [
  { title: '验证码登录', code: '20' },
  { title: '账号登录', code: '10' }
]
const curLoginMethod = ref('20')

const form = reactive({
  userName: '',
  password: '',
  verifyCode: ''
})

const route = useRoute()
const redirect = route.query.redirect || ''

const passwordType = ref('password')
const userStore = useUserStore();
const router = useRouter();

const initSeconds = ref(61)
const smsSeconds = ref(61)

const sendDisabled = computed(() => smsSeconds.value < initSeconds.value)
const loginBtnDisabled = computed(() => {
  if(!form.userName) {
    return true
  }
  if(curLoginMethod.value === '10' && !form.password) {
    return true
  }
  if(curLoginMethod.value === '20' && !form.verifyCode) {
    return true
  }

  return false
})

const sendText = computed(() => {
  if(smsSeconds.value === initSeconds.value) {
    return '发送验证码'
  }
  return `剩余${ smsSeconds.value }秒`
})

const sendCode = () => {
  // 校验手机号
  if (!/^1[3456789]\d{9}$/.test(form.userName)) {
    showToast('请输入正确的手机号')
    return
  }

  // 发送短信验证码...
  const params = {
    mobile: form.userName,
    scene: 1
  }
  sendSmsCode(params).then(data => {
    showToast('短信验证码发送成功')
    const func = () => {
      if (smsSeconds.value === 0) {
        smsSeconds.value = initSeconds.value
        return
      }
      smsSeconds.value--
      setTimeout(func, 1000)
    }
    smsSeconds.value--
    setTimeout(func, 1000)
  }).catch(e => {
    smsSeconds.value = initSeconds.value
  })
}

const doLogin = () => {
  if (!/^1[3-9]\d{9}$/.test(form.userName)) {
    showToast("手机号格式不正确");
    return
  }
  if (curLoginMethod.value === '10') {
    if (form.password.length < 4) {
      showToast("密码长度不正确");
      return
    }
    userStore.userLogin({
      mobile: form.userName,
      password: encryptSm2(form.password),
      crypto: 'sm2'
    }).then(() => {
      sessionStorage.setItem('loginWay', 'account')
      if (redirect) {
        router.replace(redirect as string)
      } else {
        // 登录成功后跳转到首页
        router.push('/home');
      }
    })
  } else {
    if (form.verifyCode.length < 4) {
      showToast("验证码长度不正确");
      return
    }
    if (!/^\d{4,6}$/.test(form.verifyCode)) {
      showToast("验证码格式不正确");
      return
    }
    userStore.smsCodeLogin({
      mobile: form.userName,
      code: form.verifyCode
    }).then(() => {
      sessionStorage.setItem('loginWay', 'account')
      if (redirect) {
        router.replace(redirect as string)
      } else {
        // 登录成功后跳转到首页
        router.push('/home');
      }
    })
  }
}

const emits = defineEmits(['backLoginFirst'])
const backLogin = () => {
  if(props.sourceTag === 'index') {
    emits('backLoginFirst')
  } else {
    router.back()
  }
}

const toFindPass = () => {
  router.push({
    path: '/login/findPassword'
  })
}

</script>

<style scoped lang="scss">
.login-container {
  background: #fdf4ef;
  height: 100vh;

  .login-bg {
    background: linear-gradient(180deg, $primary-color 0%, #fdf4ef 100%);
    height: 351px;

    &:after {
      content: '';
      display: block;
      width: 100%;
      height: 100%;
      background: url('@/assets/login/login-bg.png') no-repeat;
      background-size: 375px 229px;
    }
  }
}

.login {
  background: #ffffff;
  border-radius: 15px;
  padding: 70px 30px 20px;
  position: absolute;
  z-index: 10;
  width: 345px;
  top: 142px;
  left: 50%;
  transform: translateX(-50%);

  .logo {
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
  }

  .formInput {
    height: 36px;
    display: flex;
    align-items: center;
    margin-top: 5px;
    padding: 0 10px;
    background: #fff;
    border: 1px solid #D9D9D9;
    border-radius: 4px;

    :deep(.van-field__control) {
      &::placeholder {
        color: #afafaf;
      }
    }
  }

  :deep(.van-button--default) {
    border: none;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;    height: 36px;
    line-height: 36px;
  }
}
</style>
