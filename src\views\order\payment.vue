<template>
  <div>
    <van-popup v-model:show="showBottom" position="bottom" round safe-area-inset-bottom class="popup">
      <div class="payment">
        <div class="flex justify-center items-end pt-28 text-black font-600 mb-30">
          <span>￥</span>
          <span class="text-32 lh-28">9999</span>
          <span>.00</span>
        </div>

        <div class="bg-white rounded-10 px-20">
          <van-radio-group v-model="paymentType" @change="paymentTypeChange">
            <van-cell-group inset :border="false">
              <van-cell @click="paymentType = item.value" v-for="(item, index) in paymentList" :key="index">
                <template #title>
                  <div class="flex items-center">
                    <img :src="item.icon" class="w-18 h-18 mr-10" />
                    <span class="text-16 font-600 lh-14 text-black">{{ item.label }}</span>
                  </div>
                </template>
                <template #right-icon>
                  <VRadio :name="item.value" />
                </template>
              </van-cell>
            </van-cell-group>
          </van-radio-group>
        </div>

        <!--        <div class="bg-white rounded-10 px-20 mt-10" :class="paymentType === 'integral' ? 'payDisabled' : ''">-->
        <!--          <van-radio-group v-model="cashType" :disabled="paymentType === 'integral'">-->
        <!--            <van-cell-group inset :border="false">-->
        <!--              <van-cell-->
        <!--                  @click="cellClick(item)"-->
        <!--                  v-for="(item, index) in cashList"-->
        <!--                  :key="index"-->
        <!--                  :clickable="false"-->
        <!--              >-->
        <!--                <template #title>-->
        <!--                  <div class="flex items-center">-->
        <!--                    <img :src="item.icon" class="w-18 h-18 mr-10" />-->
        <!--                    <span class="text-16 font-600 lh-14 text-black label">{{ item.label }}</span>-->
        <!--                  </div>-->
        <!--                </template>-->
        <!--                <template #right-icon>-->
        <!--                  <VRadio :name="item.value" />-->
        <!--                </template>-->
        <!--              </van-cell>-->
        <!--            </van-cell-group>-->
        <!--          </van-radio-group>-->
        <!--        </div>-->

        <!--        <div class="bg-white rounded-10 p-20 mt-10 text-black lh-14">-->
        <!--          <div class="flex items-center justify-between">-->
        <!--            <span>合计支付</span>-->
        <!--            <span>¥600.88</span>-->
        <!--          </div>-->
        <!--          <div class="flex items-center justify-between mt-20">-->
        <!--            <span>积分支付</span>-->
        <!--            <span>-¥600.88</span>-->
        <!--          </div>-->
        <!--          <div class="flex items-center justify-between mt-20">-->
        <!--            <span>现金支付</span>-->
        <!--            <span class="color-primary text-17 font-600 lh-17">-¥600.88</span>-->
        <!--          </div>-->
        <!--        </div>-->

        <div class="px-15 py-10">
          <van-button block round class="btn-primary" @click="checkPay">确认付款</van-button>
        </div>
      </div>
    </van-popup>

    <van-dialog v-model:show="showDialog" title="积分不足" :show-confirm-button="false">
      <div class="text-center text-black font-500 lh-24">可选取其他付款方式混合支付</div>
      <div class="flex-center p-20">
        <van-button style="font-size: 16px" block plain round color="#999999" @click="showDialog = false"
          >残忍离开</van-button
        >
        <van-button style="margin-left: 15px; font-size: 16px" block round class="btn-primary" @click="showDialog = false"
          >前往支付</van-button
        >
      </div>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { showToast } from 'vant'
import { computed, ref } from 'vue'

import Alipay from '@/assets/order/Alipay.png'
import jifen from '@/assets/order/jifen.png'
import payMoney from '@/assets/order/payMoney.png'
import UnionPay from '@/assets/order/UnionPay.png'
import Wechat from '@/assets/order/Wechat.png'
import xianjin from '@/assets/order/xianjin.png'
import VRadio from '@/components/v-radio/index.vue'

defineOptions({
  name: 'Payment'
})

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['update:show'])

const paymentType = ref('')
const cashType = ref('')
const paymentList = [
  // {
  //   value: 'integral',
  //   label: '积分支付',
  //   icon: jifen
  // },
  // {
  //   value: 'cash',
  //   label: '现金支付',
  //   icon: xianjin
  // }
  {
    value: 'cash',
    label: '校园安心付',
    icon: payMoney
  }
]
const cashList = [
  {
    label: '微信',
    value: 'Wechat',
    icon: Wechat
  },
  {
    label: '支付宝',
    value: 'Alipay',
    icon: Alipay
  },
  {
    label: '云闪付',
    value: 'UnionPay',
    icon: UnionPay
  }
]

const showBottom = computed({
  get: () => props.show,
  set: (val) => {
    emits('update:show', val)
  }
})

const paymentTypeChange = (val) => {
  if (val === 'integral') {
    cashType.value = ''
  }
}

const cellClick = (item) => {
  if (paymentType.value === 'integral') {
    return
  }
  cashType.value = item.value
}

const showDialog = ref(false)

const checkPay = () => {
  // showDialog.value = true
  if (!paymentType.value) {
    return showToast('请选择先勾选校园安心付')
  }
}
</script>

<style scoped lang="scss">
.popup {
  background: #f2f2f2;
}
:deep(.van-cell-group) {
  margin: 0;
  .van-cell {
    padding: 18px 0;
    &:after {
      left: 0;
      right: 0;
    }
  }
}

.payDisabled {
  .label {
    color: #999;
    font-weight: 400;
  }
}

:deep(.van-dialog__header) {
  padding-top: 20px;
  margin-bottom: 10px;
  font-size: 20px;
}
</style>
