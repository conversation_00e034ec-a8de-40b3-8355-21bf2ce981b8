<template>
  <div class="mb-74">
    <van-sticky>
      <NavBar title="申请售后" class="!bg-white" />
    </van-sticky>
    <div class="p-10 bg-white rounded-10 mt-10">
      <OItem :orderItem="orderItemInfo"/>
    </div>

    <van-form ref="formRef" input-align="right" error-message-align="right" class="mt-20">
      <van-cell-group inset>
        <van-field name="售后类型" label="售后类型" placeholder="售后类型">
          <template #input>
            <van-radio-group v-model="form.way" direction="horizontal">
              <VRadio :name="10" size="14">仅退款</VRadio>
              <VRadio :name="20" size="14">退货退款</VRadio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field  name="售后商品数量" label="售后商品数量" placeholder="售后商品数量">
          <template #input>
            <van-stepper v-model="form.count" input-width="27px" :min="1" :max="maxCount" />
          </template>
        </van-field>
        <van-field
          v-model="form.contact"
          name="联系人"
          label="联系人"
          placeholder="联系人"
          :rules="[{ required: true, message: '请填写联系人' }]"
        />
        <van-field
          v-model="form.phone"
          type="tel"
          name="联系电话"
          label="联系电话"
          placeholder="联系电话"
          maxlength="11"
          :rules="[
            { required: true, message: '请填写联系电话' },
            { pattern: /^1\d{10}$/, message: '请填写正确的手机号' }
          ]"
        />

        <van-field
          v-model="form.applyReason"
          :formatter="applyReasonFormatter"
          readonly
          name="售后原因"
          label="售后原因"
          placeholder="售后原因"
          :rules="[{ required: true, message: '请填写售后原因' }]"
          @click="showreason = true"
        />

        <van-field
          v-model="form.refundPrice"
          type="number"
          readonly
          name="退款金额"
          label="退款金额"
          placeholder="退款金额"
          :rules="[{ required: true, message: '请填写退款金额' }]"
        />

        <van-field
          class="content"
          v-model="form.applyDescription"
          label-align="top"
          input-align="left"
          error-message-align="right"
          rows="5"
          :autosize="{ maxHeight: 109 }"
          label="问题描述"
          :border="false"
          type="textarea"
          maxlength="200"
          placeholder="请在此描述原因..."
          show-word-limit
        />
        <van-field name="applyPicUrls" label="图片上传" label-align="top" input-align="left">
          <template #input>
            <van-uploader v-model="applyPicUrls" multiple :after-read="afterRead" :before-read="beforeRead" />
          </template>
        </van-field>
      </van-cell-group>
    </van-form>

    <div class="px-15 py-10 position-fixed w-full bottom-0 bg-[#F2F2F2] lh-16 bottom van-safe-area-bottom">
      <div class="p-15">
        <van-button class="btn-primary" :disabled="uploading" round block @click="onSubmit"> 提交申请 </van-button>
      </div>
    </div>

    <van-action-sheet
      v-model:show="showreason"
      :actions="reasonActions"
      cancel-text="取消"
      close-on-click-action
      @select="selectHandle"
    />

  </div>
</template>

<script setup lang="ts">
import { closeToast,showLoadingToast,showSuccessToast,showToast } from 'vant'
import { computed, onMounted, reactive, ref, watchEffect } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { afterOrderItemSaleDetail,afterSaleCreate } from '@/api/afterSale'
import { fileUpload } from '@/api/file'
import NavBar from '@/components/navbar/index.vue'
import VRadio from '@/components/v-radio/index.vue'
import { AFTER_SALE_REASON_LIST } from '@/utils/orderUtils'
import OItem from '@/views/sales/components/oItem.vue'

defineOptions({
  name: 'SalesApply'
})

const showreason = ref(false)
const route = useRoute()
const router = useRouter()

const orderItemInfo = ref({})

const orderNo = route.query.orderNo
const orderItemId = route.query.orderItemId

const applyReasonFormatter = (value) => AFTER_SALE_REASON_LIST.find(item => item.value === value)?.name || value

const maxCount = computed(() => {
  const count = orderItemInfo.value.count - orderItemInfo.value.afterSaleCount
  return count > 0 ? count : 0
})

const uploading = ref(false)
const applyPicUrls = ref([])
const form = reactive({
  orderItemId: '',
  way: 10,
  count: 1,
  refundPrice: '',
  applyDescription: '',
  applyReason: '',
  contact: '',
  phone: ''
})

const reasonActions = ref([])

const selectHandle = (action) => {
  form.applyReason = action.value
}

const formRef = ref()

const beforeRead = (file) => {
  if (!file.type.includes('image/')) {
    showSuccessToast('请上传格式为图片的文件')
    return false
  }
  uploading.value = true
  showLoadingToast({
    duration: 0,
    forbidClick: true,
    loadingType: 'spinner',
    message: '上传中',
  })
  return true
}

const afterRead = (file) => {
  const formData = new FormData()
  formData.append('file', file.file)
  fileUpload(formData).then((res) => {
    file.url = res
    uploading.value = false
    closeToast()
  })
}

const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      afterSaleCreate({
        ...form,
        orderItemId: route.query.orderItemId,
        applyPicUrls: applyPicUrls.value ? applyPicUrls.value.map((item) => item.url) : null
      }).then(() => {
        showToast('提交成功，请您等待商家审核处理.')
        router.go(-1)
      })
    })
    .catch((err: any) => {
      console.log(err)
    })
}

const getOrderItemInfo = () => {
  afterOrderItemSaleDetail({
    orderNo: orderNo,
    orderItemId: orderItemId
  }).then((res) => {
    orderItemInfo.value = res
    orderItemInfo.value.orderNo = orderNo
  })
}

watchEffect(() => {
  form.refundPrice = (form.count * orderItemInfo.value?.skuPrice || 0).toFixed(2)

})

const fillForm = () => {
  if(route.query.fromInspect) {
    const key = 'jcy-after-sale'
    let cacheForm = sessionStorage.getItem(key)
    if(cacheForm) {
      cacheForm = JSON.parse(cacheForm)
      Object.assign(form, cacheForm)
      sessionStorage.removeItem(key)
    }
  }
}

onMounted(() => {
  getOrderItemInfo()
  fillForm()
})

watchEffect(() => {
  reasonActions.value = AFTER_SALE_REASON_LIST.filter((item) => item.way.includes(form.way))
})
</script>

<style scoped lang="scss">
.line {
  border-bottom: 1px solid #f2f2f2;
}

:deep(.van-stepper) {
  .van-stepper__input {
    width: 27px;
    height: 20px;
    margin-left: 10px;
    margin-right: 10px;
    border-radius: 2px;
  }
  .van-stepper__minus,
  .van-stepper__plus {
    width: 16px;
    height: 16px;
    background: none;
    color: #000000;
  }
  .van-stepper__minus--disabled,
  .van-stepper__plus--disabled {
    color: #d9d9d9;
  }
}

:deep(.van-cell-group--inset) {
  margin: 0;
}

:deep(.content.van-cell) {
  .van-field__control {
    background: #f2f2f2;
    border-radius: 5px;
    padding: 14px;
    color: #000000;
    font-weight: 600;
    &::placeholder {
      color: #999999;
      font-weight: 400;
    }
  }
}
</style>
