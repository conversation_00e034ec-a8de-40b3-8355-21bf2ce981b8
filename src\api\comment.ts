import request from '@/utils/request'
import type { File } from 'buffer';

const requestP = (data: any) => request({
    ...data,
    ...{
      baseURL: import.meta.env.VITE_BASE_PRODUCT_API
    }
  })
  
  const requestB = (data: any) => request({
    ...data,
    ...{
      baseURL: import.meta.env.VITE_BASE_TRADE_API
    }
  })

  const requestT = (data: any) => request({
    ...data,
    ...{
      baseURL: import.meta.env.VITE_BASE_File_API
    }
  })

// 创建评价
export function createComment(data: any) {
  return requestP({
    url: "/comment/create",
    method: "post",
    data,
  });
}

// 获取商品评价
export function getProductCommentPage(params: any) {
  return requestP({
    url: "/comment/getProductCommentPage",
    method: "get",
    params,
  });
}

// 上传图片
export function uploadFile(file: File) {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("length", file.size);

  return requestT({
    url: "/file/upload",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

// 点赞
export function like(params: any) {
  return requestP({
    url: "/comment/like",
    method: "get",
    params,
  });
}
