*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
}

::-webkit-scrollbar {
  width: 0;
  display: none;
}

body {
  min-height: 100vh;
  color: $color-text-1;
  background: $color-bg-1;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: 14px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
#app {
  //max-width: 750px;
  margin: 0 auto;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-flow: column;
  color: #000000;
  padding-bottom: constant(safe-area-inset-bottom); /* 适配安全区域 iOS < 11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* 适配安全区域 iOS >= 11.2 */
}

// 调整vant主色调
:root:root {
  --van-primary-color: #{$primary-color};
  --van-text-color: #{$color-text-1};
}

.color-primary {
  color: $primary-color;
}

.bg-primary {
  color: $primary-color;
}

.btn-primary {
  color: #fff!important;
  background-color: $primary-color!important;
}

.color-text {
  color: $color-text-1;
}

.van-sticky--fixed {
  z-index: 200!important;
}
