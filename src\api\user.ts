import request from '@/utils/request'

const requestM = (data: any) =>
  request({
    ...data,
    ...{
      baseURL: import.meta.env.VITE_BASE_MEMBER_API
    }
  })

// 获得基本信息
export function getUserInfo(params: any) {
  return requestM({
    url: '/user/get',
    method: 'get',
    params
  })
}

// 修改密码
export function changePassword(data: any) {
  return requestM({
    url: '/user/update-password',
    method: 'post',
    data: data
  })
}

// 获得会员虚拟账户
export function userAccount() {
  return requestM({
    url: '/user-account/get',
    method: 'get'
  })
}

// 获得会员虚拟账户使用记录
export function usagePage(params: any) {
  return requestM({
    url: '/user-account/usage/page',
    method: 'get',
    params
  })
}

// 获得会员虚拟账户充值记录
export function recharge(params: any) {
  return requestM({
    url: '/user-account/recharge/page',
    method: 'get',
    params
  })
}