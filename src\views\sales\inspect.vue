<template>
  <div :class="{ 'mb-64': orderItemData.supplierType !== 1 }">
    <van-sticky>
      <NavBar title="查看售后" class="!bg-white" />
      <div class="payType w-full h-100 px-20 py-26 flex items-center">
        <img :src="money" class="size-48" />
        <div class="flex flex-col ml-10 text-white">
          <div class="font-600 text-20 lh-20">{{ statusDics[afterSaleInfo.status] }}</div>
          <div class="font-200 text-14 lh-20 mt-10" v-if="afterSaleInfo.status === 20">请您尽快填写退回商品的物流信息</div>
          <div class="font-200 text-14 lh-20 mt-10" v-if="afterSaleInfo.status === 62">{{afterSaleInfo.auditReason}}</div>
          <div class="font-200 text-14 lh-20 mt-10" v-if="afterSaleInfo.status === 63">{{afterSaleInfo.receiveReason}}</div>
        </div>
      </div>
    </van-sticky>
    <div class="p-10 bg-white rounded-10 mt-10">
      <OItem :orderItem="orderItemData"/>
    </div>

    <van-cell-group title="退货物流信息" v-if="afterSaleInfo.logisticsName">
      <van-cell title="物流公司" :value="afterSaleInfo.logisticsName" />
      <van-cell title="快递单号" :value="afterSaleInfo.logisticsNo" />
      <van-cell title="发货时间"   :value="formatDateTime(afterSaleInfo.deliveryTime)" />
    </van-cell-group>

    <van-cell-group title="售后申请信息">
      <van-cell title="申请时间" :value="formatDateTime(afterSaleInfo.createTime) " />
      <van-cell title="售后商品" :value="afterSaleInfo.spuName" />
      <van-cell title="售后方式" :value="getAfterSaleWayLabel(afterSaleInfo.way)" />
      <van-cell title="联系人"   :value="afterSaleInfo.contact" />
      <van-cell title="电话"     :value="afterSaleInfo.phone" />
      <van-cell title="售后原因" :value="getAfterSaleReasonLabel(afterSaleInfo.applyReason) " />
      <van-cell title="退款金额" :value="formatMoney(afterSaleInfo.refundPrice) " />
      <van-cell title="售后说明" :value="afterSaleInfo.applyDescription" />
      <van-cell title="补充图片">
        <template #extra>
          <div class="mt-5" style="width:79%;">
          <van-image
            v-for="(item, index) in afterSaleInfo.applyPicUrls" :key="index"
            :src="item" class="mr-10" width="80" height="80" radius="4" @click="previewImg(index)"
          />
         </div>
        </template>
      </van-cell>
    </van-cell-group>

    <div class="px-15 py-10 position-fixed w-full bottom-0 bg-[#F2F2F2]" v-if="orderItemData.supplierType !== 1">
      <van-row gutter="20" v-if="[20].includes(afterSaleInfo.status) && orderItemData.orderStatus !== 9">
        <van-col span="12">
          <van-button class="btn-primary" round block @click="cancelApply">取消申请</van-button>
        </van-col>
        <van-col span="12">
          <van-button class="btn-primary" round block @click="goDeliveryInfo">补充发货信息</van-button>
        </van-col>
      </van-row>

      <van-button class="btn-primary" round block v-if="[10].includes(afterSaleInfo.status) && orderItemData.orderStatus !== 9" @click="cancelApply">取消申请</van-button>
      <van-button v-if="[62,63].includes(afterSaleInfo.status) && orderItemData.orderStatus !== 9" class="btn-primary" round block @click="cotinueApply(1)"> 继续申请 </van-button>
      <van-button v-if="[50].includes(afterSaleInfo.status) && orderItemData.canAfterSale" class="btn-primary" round block @click="cotinueApply(2)"> 继续申请 </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { showConfirmDialog, showImagePreview,showToast } from 'vant'
import { onMounted,ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { afterOrderItemSaleDetail, afterSaleCancel } from '@/api/afterSale'
import money from '@/assets/order/money.png'
import NavBar from '@/components/navbar/index.vue'
import { formatMoney } from '@/utils/base'
import { formatDateTime,getAfterSaleReasonLabel, getAfterSaleWayLabel } from '@/utils/orderUtils'
import OItem from '@/views/sales/components/oItem.vue'

defineOptions({
  name: 'AfterInspect'
})

const route = useRoute()
const router = useRouter()

const statusDics = {
  10: '等待卖家审核',
  20: '等待您退货',
  30: '等待卖家收货',
  40: '等待平台退款',
  50: '退款完成',
  62: '卖家拒绝了您的售后申请',
  63: '卖家拒绝收货'
}

const afterSaleInfo = ref({})
const orderItemData = ref({})

const previewImg = (index: number) => {
  showImagePreview({
    images: afterSaleInfo.value.applyPicUrls,
    startPosition: index
  })
}

const cancelApply = () => {
  showConfirmDialog({
    title: '提示',
    message: '确定取消申请吗？'
  })
    .then(async () => {
      await afterSaleCancel({ no: afterSaleInfo.value.no })
      showToast('取消售后成功')
      router.back()
    })
    .catch(() => {
      // on cancel
    })
}

const cotinueApply = (type: any) => {
  if(type === 1) {
    const params = {
      way: afterSaleInfo.value.way,
      count: afterSaleInfo.value.count,
      refundPrice: afterSaleInfo.value.refundPrice,
      applyDescription: afterSaleInfo.value.applyDescription,
      applyReason: afterSaleInfo.value.applyReason,
      contact: afterSaleInfo.value.contact,
      phone: afterSaleInfo.value.phone,
      applyPicUrls: afterSaleInfo.value.applyPicUrls
    }
    if(params.applyPicUrls) {
      params.applyPicUrls = params.applyPicUrls.map(item => ({
        url: item
      }))
    }
    sessionStorage.setItem('jcy-after-sale', JSON.stringify(params))
  }

  router.push({
    name: 'SalesApply',
    query: {
      orderItemId: route.query.orderItemId,
      orderNo: route.query.orderNo,
      fromInspect: 1
    }
  })
}

const getAfterOrderItemSaleDetail = async () => {
  const res = await afterOrderItemSaleDetail({
    orderNo: route.query.orderNo,
    orderItemId: route.query.orderItemId
  })
  orderItemData.value = res
  orderItemData.value.orderNo = route.query.orderNo
  afterSaleInfo.value = res.afterSaleInfo || {}
}

const goDeliveryInfo = () => {
  router.push({
    path: '/sales/delivery-info',
    query: route.query
  })
}

onMounted(() => {
  getAfterOrderItemSaleDetail()
})
</script>

<style scoped lang="scss">
.payType {
  background: url('@/assets/order/group.png') no-repeat center;
  background-size: 100% 100%;
}

.bottom {
  box-shadow: 0px 2px 20px 0px rgba(174, 174, 174, 0.5);
}
</style>
