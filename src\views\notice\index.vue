<template>
  <div>
    <van-sticky>
      <NavBar title="公告列表" />
    </van-sticky>

    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <div class="px-20 rounded-10 bg-white page">
        <van-list
          v-if="list.length"
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
          :immediate-check="false"
        >
          <div class="py-10 item flex items-center justify-between"
            v-for="(item, idx) in list" :key="idx"  @click="goDetail(item)"
          >
            <div class="flex items-center mr-20">
              <div class="rounded-[50%] size-4 bg-primary mr-10 flex-shrink-0"></div>
              <div class="flex flex-col lh-17">
                <div class="ellipsis-1">{{ item.title }}</div>
              </div>
            </div>
            <div class="text-[#999] text-12 lh-17 flex-shrink-0">{{ getDate(item.publishTime) }}</div>
          </div>
        </van-list>
        <EmptyBox v-else />
      </div>
    </van-pull-refresh>
  </div>
</template>

<script setup lang="ts">
import { contentList } from '@/api/notice'

defineOptions({
  name: 'NoticeIndex'
})
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

import EmptyBox from '@/components/emptyBox/index.vue'
import NavBar from '@/components/navbar/index.vue'
import { getDate } from '@/utils/base'

const router = useRouter()
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const page = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

const list = ref([{}, {}])

const getData = () => {
  loading.value = true
  contentList({
    page: page.current,
    pageSize: page.pageSize,
    categoryType: 20
  }).then((res) => {
    if (refreshing.value) {
      list.value = []
      refreshing.value = false
    }
    loading.value = false
    if (page.current === 1) {
      list.value = res?.list || []
    } else {
      list.value = list.value.concat(res.list)
    }
    page.total = res?.total || 0
    finished.value = list.value.length >= page.total
  }).catch(() => {
    loading.value = false
    finished.value = true
  })
}

const onLoad = () => {
  page.current++
  getData()
}

const goDetail = (item: any) => {
  router.push({
    name: 'NoticeDetail',
    query: {
      id: item.id
    }
  })
}

const onRefresh = () => {
  page.current = 1
  // 清空列表数据
  finished.value = false

  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  getData()
}

onMounted(() => {
  getData()
})
</script>

<style scoped lang="scss">
.page {
  min-height: calc(100vh - 46px);
}
.item {
  border-bottom: 1px solid #f2f2f2;
}
</style>
