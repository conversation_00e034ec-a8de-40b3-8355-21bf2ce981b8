<template>
  <div>
    <div class="mb-12 text-black font-600 lh-16">{{ orderItem.supplierName }}</div>
    <div class="flex items-center justify-between text-10 lh-17">
      <div class="text-[#999999]">订单号：{{ orderItem.orderNo }}</div>
    </div>
    <div class="flex flex-col mt-10">
      <div class="flex">
        <img :src="orderItem.picUrl" class="size-94 rounded-5 flex-shrink-0" />
        <div class="ml-10 flex flex-col flex-1">
          <div class="flex items-center justify-between">
            <div class="font-600 text-14 text-black ellipsis-2 flex-1">
              {{ orderItem.skuName }}
            </div>
            <div class="text-[#6D6C6C] text-12 lh-14 w-46 flex-shrink-0 text-right">
              x{{ orderItem.count }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex items-center justify-end text-12 text-[#999999] mt-12">
      <div>单价￥{{ orderItem.skuPrice }}</div>
      <div class="ml-10">总价￥{{ orderItem.skuTotalPrice }}</div>
    </div>
  </div>

</template>

<script setup lang="ts">
defineOptions({
  name: 'OItem'
})

const props = defineProps({
  orderItem: {
    type: Object,
    default: () => {}
  }
})

</script>

<style scoped lang="scss"></style>
