import request from '@/utils/request'

const requestM = (data: any) => request({
  ...data,
  ...{
    baseURL: import.meta.env.VITE_BASE_MALL_API
  }
})

const requestT = (data: any) => request({
  ...data,
  ...{
    baseURL: import.meta.env.VITE_BASE_TRADE_API
  }
})

// 客服服务
export function getServiceList(params: {
  pageNo: number,
  pageSize: number
}) {
  return requestM({
    url: '/supplier/get-service-list',
    method: 'get',
    params
  })
}

// 意见反馈
export function userFeedback(data: {
  content: string
}) {
  return requestM({
    url: '/user-feedback/save',
    method: 'post',
    data
  })
}

// 分页查询收藏商品
export function getCollectPage(params: {
  pageNo: number,
  pageSize: number
}) {
  return requestT({
    url: '/collect/getCollectPage',
    method: 'get',
    params
  })
}

// 取消收藏商品
export function cancelCollect(params: string) {
  return requestT({
    url: '/collect/cancelCollect',
    method: 'post',
    params
  })
}

// 批量取消收藏商品
export function batchCancel(data: any) {
  return requestT({
    url: '/collect/batchCancel',
    method: 'post',
    data
  })
}
