/**
 * Checks if the user agent is a WeChat browser.
 *
 * @returns {boolean} True if the user agent is a WeChat browser, false otherwise.
 */
export function is_weixn(): boolean {
  // Get the user agent string and convert it to lowercase
  const userAgent = navigator.userAgent.toLowerCase()
  const matchResult = userAgent.match(/MicroMessenger/i)

  // Check if the user agent contains "micromessenger"
  return !!(matchResult && matchResult[0] === 'micromessenger')
}
