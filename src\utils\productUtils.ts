import { useHomeStore } from '@/stores/home'

export const showFieldEnable = (name: string) => {
  const useHome = useHomeStore()
  const configData = useHome.configInfo.configData || {}
  return configData.productField && configData.productField.indexOf(name) >= 0
}

export const enableMarketPrice = () => showFieldEnable('market-price')
export const enableSupName = () => showFieldEnable('list-supplier')

export const isStockValid = (state: number) => [33, 36, 39, 40].includes(state)

export const isOutStock = (state: number) => [0, 34, 99].includes(state)