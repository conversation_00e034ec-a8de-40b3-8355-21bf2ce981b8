<template>
  <div id="goods-page" class="goods rounded-10 bg-white">
    <van-sticky>
      <NavBar title="商品详情"></NavBar>
    </van-sticky>
    <van-loading v-if="loading" class="loading-wrap" color="#1989fa" />
    <template v-else>
      <div class="pb-50 goods-wrap">
        <div class="switch-wrapper top-57 left-5 w-100 h-28 p-2 rounded-15 z-1000">
          <div class="switch-item rounded-15 w-48 h-24 lh-24" @click="scrollPage('goods')"
            :class="{ 'switch-check': switchFlag === 'goods' }">商品</div>
          <div class="switch-item rounded-15 w-48 h-24 lh-24" @click="scrollPage('detail')"
            :class="{ 'switch-check': switchFlag === 'detail' }">详情</div>
        </div>
        <div id="goods">
          <img width="100%" height="400" :src="detailInfo.imagePath" />
          <div class="rounded-20 bg-white pt-18">
            <div class="pr-10 pl-10">
              <div v-if="detailInfo.salePrice != -1">
                <div class="color-primary text-14">
                  采购价￥<span class="text-24 font-600">{{ saveTwoDecimal(detailInfo.salePrice) }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <div class="color-primary text-14">
                    原价<span class="decoration-line-through">￥{{ saveTwoDecimal(detailInfo.marketPrice) }}</span>
                  </div>
                  <!-- <div class="color-#666666 text-12">销量：999+</div> -->
                </div>
              </div>
              <div v-else>
                <div class="color-#989898 font-500 text-14 my-6">登录后查看价格</div>
              </div>
              <div class="text-17 color-text font-600 detail-title">
                {{ detailInfo.skuName }}
              </div>
            </div>

            <div class="bg-#F2F2F2 rounded-10">
              <!-- <div class="flex items-center justify-around pt-18 pb-18">
                <div v-for="item in list" class="w-100 h-50 p-8 bg-white rounded-4 flex items-center justify-between">
                  <img :src="item.img" width="28" height="28"/>
                  <div>
                    <div class="text-14 color-text">{{ item.name }}</div>
                    <div class="text-12 color-primary">￥{{ item.price }}</div>
                  </div>
                </div>
              </div> -->

              <div class="bg-white p-10 rounded-10 pb-0">
                <!-- <div class="pb-16 check-wrap h-58 flex">
                  <div class="color-#6C6C6C text-14 w-40 mr-6">已选</div>
                  <div class="checked-info text-14 color-text">
                    78L/闪亮银/一级能效 /冷藏冷冻 1件 售后服务服务，可选增值服务
                  </div>
                  <van-icon class="ml-6 mt-3" name="arrow" />
                </div> -->
                <div class="pt-16 h-58 flex border-line" @click="toChangeAddress">
                  <div class="color-#6C6C6C text-14 w-40 mr-6">送至</div>
                  <div class="checked-info text-14 color-text flex-1">
                    <div class="overflow-hidden-text">{{ getAddress(addressInfo) }}</div>
                    <div class="text-14 overflow-hidden-text" v-if="deliveryPredictInfo" v-html="deliveryPredictInfo">
                    </div>
                  </div>
                  <van-icon class="ml-6 mt-3" name="arrow" />
                </div>
                <div class="h-44 flex justify-between items-center border-line">
                  <div class="color-#6C6C6C text-14">供应商</div>
                  <div class="color-text000 text-14">{{ detailInfo.supplierName || '--' }}</div>
                </div>
                <div class="h-44 flex justify-between items-center">
                  <div class="color-#6C6C6C text-14">商品分类</div>
                  <div class="color-text000 text-14">{{ detailInfo.categoryNamePath || '--' }}</div>
                </div>
                <div class="h-44 flex justify-between items-center">
                  <div class="color-#6C6C6C text-14">品牌</div>
                  <div class="color-text000 text-14">{{ detailInfo.brandName || '--' }}</div>
                </div>
                <div class="h-44 flex justify-between items-center" v-if="detailInfo.returnRuleStr">
                  <div class="color-#6C6C6C text-14">温馨提示</div>
                  <div class="color-text000 text-14">{{ detailInfo.returnRuleStr }}</div>
                </div>
              </div>

              <div class="bg-white p-10 rounded-10 mt-10">
                <!-- <div class="color-primary text-14">您的同事还买过</div> -->
                <div class="color-primary text-14 mb-16">商品推荐</div>
                <div class="flex flex-wrap">
                  <div v-for="item in suggestList.slice(0, 6)" class="w-33.3%" @click="toSuggestDetail(item)">
                    <img :src="item.imageUrl" width="112" height="112" class="rounded-10" />
                    <div>
                      <div class="overflow-hidden-text text-12 color-text">{{ item.skuName }}</div>
                      <div v-if="item.salePrice != -1" class="color-primary text-12">￥{{ saveTwoDecimal(item.salePrice) }}</div>
                      <div v-else class="color-#989898 font-500 text-12 my-6">登录后查看价格</div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="configData.productCommentSwitch" class="bg-white p-10 rounded-10 mt-10">
                <div class="h-32 flex justify-between items-center border-b border-gray-300" @click="toCommentDetail">
                  <div class="color-#6C6C6C text-14">商品评价 ({{ `${commentTotal}` }})</div>
                  <van-icon v-if="commentTotal > 0" class="text-gray-400" name="arrow" />
                  <div v-if="commentTotal == 0" class="color-text000 text-14">暂无评价</div>
                </div>
                <div v-if="commentTotal > 0" class="mt-10">
                  <div v-for="(review, index) in commentList" :key="index" class="mb-4">
                    <div class="flex items-center justify-between mb-2 px-2">
                      <div class="flex items-center">
                        <van-icon v-show="review.avatar != null" :name="review.avatar" class="mr-2" />
                        <span class="text-gray-500 text-12 mr-10">{{ formatName(review.nickName) }}</span>
                        <van-rate v-model="review.score" size="14px" readonly class="text-red-500" />
                      </div>
                      <div class="text-gray-400 text-12">{{ formatDate(review.createTime) }}</div>
                    </div>
                    <div class="text-gray-600 mb-2 text-12 px-2 pt-10">
                      {{ review.content }}
                    </div>
                    <div class="flex space-x-10 mb-2 pt-10">
                      <van-image v-for="(img, imgIndex) in review && review.pics ? review.pics.split(',') : []"
                        :key="imgIndex" :src="img" width="60" height="60" @click="previewImg(imgIndex)" />
                      <van-image-preview v-model:show="showPreview" :images="review && review.pics ? review.pics.split(',') : []"
                        :startPosition="currentImageIndex" :closeable="true" />
                    </div>
                    <van-divider v-if="index < commentList.length - 1"
                      :style="{ borderColor: '#C3C3C3', padding: '0 2px' }" />
                  </div>
                  <div v-if="commentTotal > 0" class="text-center color-gray text-12 mt-30 mb-2" @click="toCommentDetail">
                    <span>查看更多评价</span>
                  </div>
                </div>
              </div>
              <div class="bg-#F2F2F2 rounded-10" id="detail">
                <div class="text-center color-text text-14 mt-10 mb-10">—— 商品详情 ——</div>
                <div v-if="detailInfo.introduceApp" class="detail-image goods-rich" v-html="detailInfo.introduceApp.replace(/https:http:\/\//g, 'https://')"></div>
                <div v-if="!detailInfo.introduceApp" class="w-100% bg-#fff p-12 goods-rich" v-html="detailInfo.introduce"></div>
                <div style="padding: 10px 0;">
                  <van-cell-group inset title="商品规格">
                    <van-cell title="商品编码" :value="pSkuId" />
                    <van-cell v-for="(pa,index) in paramAttributeList" :key="index" :title="pa.pname" :value="pa.pvalue" />
                  </van-cell-group>
                </div>
                <div v-if="bookExtInfo">
                  <van-cell title="出版社" :value="bookExtInfo.publishers" />
                  <van-cell title="ISBN" :value="bookExtInfo.isbn" />
                  <van-cell v-if="bookExtInfo.batchNo" title="版次" :value="bookExtInfo.batchNo" />
                  <van-cell title="商品编码" :value="detailInfo.skuInnerId" />
                  <van-cell v-if="bookExtInfo.publishers" title="品牌" :value="bookExtInfo.bookBrand || bookExtInfo.publishers" />
                  <van-cell v-if="bookExtInfo.bookPackage" title="包装" :value="bookExtInfo.bookPackage" />
                  <van-cell title="开本" value="16开" />
                  <van-cell v-if="bookExtInfo.publishTime" title="出版时间" :value="bookExtInfo.publishTime" />
                  <van-cell v-if="bookExtInfo.papers" title="用纸" :value="bookExtInfo.papers" />
                  <van-cell v-if="bookExtInfo.pagesNumber" title="页数" :value="bookExtInfo.pagesNumber" />
                  <van-cell v-if="bookExtInfo.language" title="正文语种" :value="bookExtInfo.language" />
                  <div v-if="bookExtInfo.productFeatures">
                    <div class="text-center color-text text-14 mt-10 mb-10">—— 产品特色 ——</div>
                    <div v-html="bookExtInfo.productFeatures" class="book-feature"></div>
                  </div>
                  <div v-if="bookExtInfo.contentDesc">
                    <div class="text-center color-text text-14 mt-10 mb-10">—— 内容简介 ——</div>
                    <div class="book-desc" v-html="bookExtInfo.contentDesc"></div>
                  </div>
                  <div v-if="bookExtInfo.editorDesc">
                    <div class="text-center color-text text-14 mt-10 mb-10">—— 作者简介 ——</div>
                    <div class="book-desc" v-html="bookExtInfo.editorDesc"></div>
                  </div>
                  <div v-if="bookExtInfo.bookCatalogue">
                    <div class="text-center color-text text-14 mt-10 mb-10">—— 目录 ——</div>
                    <div class="book-desc" v-html="bookExtInfo.bookCatalogue"></div>
                  </div>
                  <div v-if="bookExtInfo.bookAbstract">
                    <div class="text-center color-text text-14 mt-10 mb-10">—— 精彩书摘 ——</div>
                    <div class="book-desc" v-html="bookExtInfo.bookAbstract"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <van-action-bar>
      <!-- <van-action-bar-icon icon="chat-o" text="客服" color="#ee0a24" /> -->
      <van-action-bar-icon v-if="!detailInfo.skuId || collectList[0] !== detailInfo.skuId" @click="toStar(true)" icon="star-o" text="未收藏" color="#ff5000" />
      <van-action-bar-icon v-else @click="toStar(false)" icon="star" text="已收藏" color="#ff5000" />
      <van-action-bar-icon color="#323233" @click="toShopcart" icon="cart-o" text="购物车" />
      <template v-if="!isOutStock(stockObj.stockStateType)">
        <van-action-bar-button :disabled="btnDisabled" @click="addCart" type="warning" text="加入购物车" />
        <van-action-bar-button :disabled="btnDisabled" @click="toBuy" type="danger" text="立即购买" />
      </template>
      <van-action-bar-button v-else color="#999999" type="warning" text="商品暂时无货" />
    </van-action-bar>

    <van-action-sheet v-model:show="show" title="选择购买数量">
      <div class="sheet-content p-15 pt-0">
        <div class="flex mb-18">
          <img width="58px" height="58px" :src="detailInfo.imagePath" />
          <div class="text-12 color-primary lh-58 ml-6">￥<span class="text-18 font-600">{{ saveTwoDecimal(detailInfo.salePrice) }}</span></div>
        </div>
        <div class="flex justify-between items-center h-30 mb-46">
          <div class="color-#6C6C6C text-14">购买数量</div>
          <van-stepper v-model="countValue" integer :min="detailInfo.lowestBuy || 1" :max="99999"/>
        </div>
        <van-button block round class="btn-primary" @click="completeHandler">完成</van-button>
      </div>
    </van-action-sheet>
  </div>
</template>

<script setup lang="ts">
import { showToast } from 'vant'
import { computed, nextTick, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { addToCollect, cancelCollect, getSkuCategoryName, getSkuDetailInfo, getSkuPredictPromise, getSkuStockInfo, getSkuSuggestList, queryCollectStatus, queryGoodsStockInfo } from '@/api/classify'
import { getProductCommentPage } from '@/api/comment'
import { addCartApi } from '@/api/shoppingCart'
import NavBar from '@/components/navbar/index.vue'
import { useHomeStore } from '@/stores/home'
import { useUserStore } from '@/stores/user'
import { formatDate } from '@/utils/orderUtils'
import { isOutStock, isStockValid } from '@/utils/productUtils'

defineOptions({
  name: 'GoodsDetail'
})

const detailInfo = ref({})
const stockObj = ref({})
const bookExtInfo = ref({})
const collectList = ref([])
const commentList = ref([])
const commentTotal = ref(0)
const userStore = useUserStore()
const addressInfo = ref(userStore.defaultAddress)
const route = useRoute()
const deliveryPredictInfo = ref('')
const loading = ref(false)
const currentImageIndex = ref<number>(0)
const showPreview = ref<boolean>(false)
const homeStore = useHomeStore()
const configData = homeStore.configInfo.configData || {}
const btnDisabled = computed(() => !detailInfo.value.skuId || !isStockValid(stockObj.value.stockStateType) || detailInfo.value.skuState != 1 || detailInfo.value.saleStatus != 1)

const queryCollect = async (skuIds: []) => {
  const data = await queryCollectStatus({
    skuIds
  })
  collectList.value = data
}

const pSkuId = computed(() => detailInfo.value.skuInnerId && detailInfo.value.skuInnerId.indexOf('mall-') >= 0 ? detailInfo.value.skuId : detailInfo.value.skuInnerId)

const parseValList = (arr) => {
  const result = arr.map(item => item[0])
  return result.join('，')
}

const paramAttributeList = computed(() => {
  const arr = []
  if(detailInfo.value.paramGroupAttrList) {
    detailInfo.value.paramGroupAttrList.forEach(paramGroup => {
      if(paramGroup.paramAttributeList) {
        paramGroup.paramAttributeList.forEach(paramAttribute => {
          arr.push({
            pname: paramAttribute.paramAttrName,
            pvalue: parseValList(paramAttribute.paramAttrValList)
          })
        })
      }
    })
  }

  return arr
})

const toStar = async (flag: boolean) => {
  const params = {
    skuId: detailInfo.value.skuId,
    supplierId: detailInfo.value.supplierId
  }
  await flag ? addToCollect(params) : cancelCollect(params)
  showToast('操作成功')
  if (flag) {
    collectList.value = [detailInfo.value.skuId]
  } else {
    collectList.value = []
  }
  setTimeout(() => {
    // 等1500秒，后台有时候反应没那么快
    queryCollect([detailInfo.value.skuId])
  }, 1500)
}

const suggestList = ref([])
const getSuggest =  async () => {
  const { skuId, supplierId } = detailInfo.value
  const data = await getSkuSuggestList({
    skuId,
    supplierId,
    queryExtSet: 1
  })
  suggestList.value = data || []
}

const queryGoodsStock = async () => {
  const data = await queryGoodsStockInfo({
    skuId: detailInfo.value.skuId,
    supplierId: detailInfo.value.supplierId,
    area: JSON.stringify({
      provinceId: addressInfo.value.provinceId,
      cityId: addressInfo.value.cityId,
      countyId: addressInfo.value.countyId,
      townId: ''
    })
  })
  stockObj.value = data
}

const queryDeliveryPredictInfo = async () => {
  if(!detailInfo.value.isJD) {
    return
  }
  const data = await getSkuPredictPromise({
    skuId: detailInfo.value.skuInnerId,
    skuNum: countValue.value,
    supplierId: detailInfo.value.supplierId,
    provinceId: addressInfo.value.provinceId,
    cityId: addressInfo.value.cityId,
    countyId: addressInfo.value.countyId,
    townId: addressInfo.value.townId || ''
  })
  deliveryPredictInfo.value = ''
  if(data) {
    deliveryPredictInfo.value = data.predictContent
  }
}

const getSkuPrice = async () => {
  const data = await getSkuStockInfo({
    skuId: detailInfo.value.skuId
  })
  if (data) {
    detailInfo.value = Object.assign(detailInfo.value, data)
  }
}

const getProductCommentList = async () => {
  const data = await getProductCommentPage({
    skuId: detailInfo.value.skuId,
    pageNo: 1,
    pageSize: 3
  })
  commentList.value = data.list || []
  commentTotal.value = data.total
}

const toCommentDetail = () => {
  if (commentTotal.value === 0) {
    return
  }
  const { supplierId, skuId } = route.query
  router.push({
    path: '/comment/index',
    query: {
      skuId: skuId,
      supplierId: supplierId
    }
  })
}

const router = useRouter()
const toShopcart = () => {
  // if (btnDisabled.value) {
  //   return
  // }
  router.push('/shoppingCart')
}

const toChangeAddress = () => {
  router.push('/address/index?type=submit-order')
}

const getAddress = (item: AppAddressRespVO) =>
  `${ item.provinceName }${ item.cityName }${ item.countyName }${ item.townName || '' }${ item.consigneeAddress || '' }`

// 保留两位小数
const saveTwoDecimal = (val = 0) => (Math.round(val * 100) / 100).toFixed(2)

// 加入购物车
const addCart = () => {
  show.value = true
  actionType = 'addCart'
}

// 立即购买
const toBuy = () => {
  show.value = true
  actionType = 'toBuy'
}

const show = ref(false)
const countValue = ref(1)
let actionType = ''

const completeHandler = async () => {
  if (actionType === 'addCart') {
    if(!detailInfo.value.skuId) {
      return
    }
    const params = {
      area: {
        provinceId: addressInfo.value.provinceId,
        cityId: addressInfo.value.cityId,
        countyId: addressInfo.value.countyId,
        townId: ''
      },
      count: countValue.value,
      skuId: detailInfo.value.skuId,
      supplierId: detailInfo.value.supplierId
    }
    if (addressInfo.value.townId) {
      params.area.townId = addressInfo.value.townId
    }
    const data = await addCartApi(params)
    if (data) {
      showToast('加入购物车成功！')
      userStore.loadCartCount()
      show.value = false
      countValue.value = 1
    }
  } else {
    router.push({
      path: '/order/orderConfirm',
      query: {
        skuId: detailInfo.value.skuId,
        supplierId: detailInfo.value.supplierId,
        addressId: addressInfo.value.id,
        count: countValue.value
      }
    })
  }
}

// 跳到推荐商品的详情页
const toSuggestDetail = (item: any) => {
  router.push({
    path: '/goods/detail',
    query: {
      skuId: item.skuId,
      supplierId: item.supplierId
    }
  })
}

const switchFlag = ref('goods')

const scrollPage = (type: string) => {
  switchFlag.value = type
  document.getElementById(type).scrollIntoView({
    behavior: 'smooth',
    block: 'start'
  })
}

const changeWrap = async () => {
  const jdDescItems = document.getElementsByClassName("ssd-module-wrap");
  if (jdDescItems.length == 1) {
    // only handle 1 item
    const jdDesc = jdDescItems[0];
    const container = jdDescItems[0].parentElement;
    // to fix width.
    if(container.offsetWidth < jdDesc.offsetWidth) {
      const targetWidth = Math.min(750, container.offsetWidth);
      const scaleValue  = targetWidth / 750;
      const fontSizeValue = Math.round(14 / scaleValue);
      jdDesc.style.transformOrigin = "0px 0px";
      jdDesc.style.transform = `scale(${   scaleValue  })`;
      jdDesc.style.fontSize = `${ fontSizeValue  }px`;
      jdDesc.style.lineHeight = "2";
      await nextTick()
      container.style.height = `${ jdDesc.offsetHeight * scaleValue  }px`
    }
  }
}

const loadCategoryName = async () => {
  const ids = detailInfo.value.category
  if(ids) {
    let segs = ids.split(";")
    const data = await getSkuCategoryName({ids: segs.join(",")})
    segs = data.split('/')
    detailInfo.value.categoryNamePath = segs.join(' / ')
  }
}

const getDetailData = async () => {
  loading.value = true
  const { supplierId, skuId } = route.query
  const data = await getSkuDetailInfo({
    skuId,
    supplierId,
    queryExtSet: 1 // 移动端
  })
  detailInfo.value = data
  if(!detailInfo.value.skuId) {
    showToast('商品信息不存在，请稍后重试。')
    return
  }
  bookExtInfo.value = data.bookExtInfo

  queryCollect([detailInfo.value.skuId])
  getSuggest()
  queryGoodsStock()
  queryDeliveryPredictInfo()
  getSkuPrice()
  getProductCommentList()
  loadCategoryName()
  loading.value = false
  await nextTick()
  changeWrap()
}

getDetailData()

watch(
  () => route.query,
  () => {
    getDetailData()
    const el = document.querySelectorAll('.goods-wrap')[0]
    if (el) {
      el.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  },
  { immediate: false, deep: true }
)

const formatName = (nickName) => {
  if (nickName != null && nickName.length > 2) {
    const startChar = nickName[0]; // 获取第一个字符
    const endChar = nickName[nickName.length - 1]; // 获取最后一个字符
    const middleStars = '*'.repeat(nickName.length - 2); // 中间部分用星号替换
    return `${ startChar }${ middleStars }${ endChar }`;
  } else if (nickName != null && nickName.length === 2) {
    return `${ nickName.substring(0, 1)  }*`;
  }
  return "匿名";

}

const previewImg = (imgIndex: number) => {
  currentImageIndex.value = imgIndex
  showPreview.value = true
}

</script>

<style scoped lang="scss">
.detail-title, .checked-info {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.check-wrap {
  border-bottom: 1px solid #EAE9E9;
}
.overflow-hidden-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.goods {
  position: relative;
  .switch-wrapper {
    position: fixed;
    background-color: #333333;
    display: flex;
    .switch-item {
      color: #ffffff;
      text-align: center;
    }
    .switch-check {
      background-color: #ffffff;
      color: #000000;
    }
  }
  .book-feature {
    :deep(img) {
      width: 100%;
      object-fit: cover;
    }
  }
  .book-desc {
    background-color: #fff;
    padding: 18px 10px 0;
    font-size: 14px;
    color: #757575;
    line-height: 24px;
  }
}
.loading-wrap {
  text-align: center;
  height: calc(100vh - 90px);
  padding-top: 100px;
}
.detail-image {
  width: 100%;
  :deep(img) {
    width: 100%;
    object-fit: cover;
    height: auto;
  }
}
.border-line {
  border-bottom: 1px solid #EAE9E9;
}
.sheet-content {
  :deep(.van-stepper) {
    .van-stepper__input {
      margin: 0 8px;
    }
    .van-stepper__minus, .van-stepper__plus {
      background-color: transparent;
    }
  }
}
</style>
<style lang="scss">
.goods-rich img {
  max-width: 100%;
}
</style>
