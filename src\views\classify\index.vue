<template>
  <div>
    <NavBar title="分类"></NavBar>
    <van-tree-select v-model:main-active-index="activeIndex" :items="categoryList" @click-nav="handleClickTree">
      <template #content>
        <div class="content">
          <div class="flex subClassify pb-10 pl-10" id="subClassify">
            <div class="tabspan flex flex-row gap-8" :class="[isRolling ? 'h-26' : 'flex-wrap']">
              <div class="tab-text" v-for="item in secondCategoryList" @click="scrollToCategory(item.categoryId)">{{ item.categoryName }}</div>
            </div>
            <div class="size-24 ml-8" @click="switchRolling">
              <img :style="{ transform: !isRolling ? 'rotate(180deg)' : 'none' }" :src="arrowDown" class="w-24 h-24" />
            </div>
          </div>

          <div class="overflow-y-auto" :style="thirdClassify">
            <div class="classifyBox" :id="'classifyBox' + second.categoryId" v-for="(second, index) in secondCategoryList" :style="{
              'border-radius': index === 0 ? '10px 10px 0 0' : index === secondCategoryList.length - 1 ? '0 0 10px 10px' : '0',
            }">
              <div class="flex items-center mb-10" @click="handleGoodsDetail(second)">
                <span class="classifyName text-16 font-500 text-black">{{ second.categoryName }}</span>
                <van-icon name="arrow" />
              </div>
              <div class="goods__list flex flex-wrap">
                <div
                    class="goods__item"
                    v-for="(item, index) in second.childCategoryList"
                    :key="index"
                    @click="handleGoodsDetail(item)"
                >
                  <div class="flex flex-col gap-5">
                    <!-- <van-image class="size-70 image" :src="item.iconUrl || 'https://img.yzcdn.cn/vant/apple-1.jpg'" /> -->
                    <div class="name ellipsis-2">{{ item.categoryName }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </van-tree-select>
  </div>
</template>

<script setup lang="ts">

import { computed,nextTick, onMounted, ref } from 'vue'
import {useRoute, useRouter} from 'vue-router'

import { getChildCategoryTreeList } from '@/api/classify'
import { getRootCategoryList } from '@/api/home'
import arrowDown from '@/assets/classify/arrow-down.png'
import NavBar from '@/components/navbar/index.vue'

const activeIndex = ref(0)
const isRolling = ref(true)
const topHeight = ref(0)

const switchRolling = () => {
  isRolling.value = !isRolling.value
  nextTick(() => {
    topHeight.value = document.querySelectorAll('#subClassify')[0].clientHeight
  })
}

// 获取分类
const categoryList = ref([])
const secondCategoryList = ref([])

const route = useRoute()

const getImgList = async () => {
  const res = await getRootCategoryList()
  categoryList.value = res.map(x => ({
    id: x.categoryId,
    text: x.categoryName
  })) || []
  if (route.query.categoryId) {
    activeIndex.value = categoryList.value.findIndex(x => String(x.id) === route.query.categoryId as string)
  } else {
    activeIndex.value = 0
  }
  handleClickTree(activeIndex.value)
}

getImgList()

const handleClickTree = async (index: number) => {
  const parentCategoryId = categoryList.value[index].id
  const res = await getChildCategoryTreeList({ parentCategoryId })
  secondCategoryList.value = res
  isRolling.value = true
}

const router = useRouter()
const handleGoodsDetail = (item: any) => {
  console.log(item)
  const queryItem = {
    categoryName: item.categoryName
  }
  const pname = `categoryId${ item.categoryLevel + 1 }`
  queryItem[pname] = item.categoryId
  router.push({
    path: '/classify/filter',
    query: queryItem
  })
}

const scrollToCategory = (id: number) => {
  const el = document.querySelectorAll(`#classifyBox${ id }`)[0]
  if (el) {
    el.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
      inline: 'nearest'
    })
  }
}

const thirdClassify = computed(() => ({
  height: `calc(100% - ${ topHeight.value }px)`
}))

onMounted(() => {
  topHeight.value = document.querySelectorAll('#subClassify')[0].clientHeight
})
</script>

<style scoped lang="scss">
:deep(.van-tree-select) {
  height: calc(100vh - 50px - 50px) !important;
  padding-top: 10px;
  .van-tree-select__nav {
    background: #f2f2f2;
  }

  .van-tree-select__content {
    background: #f2f2f2;
    flex: inherit;
    flex-shrink: 0;
    padding-right: 10px;
    overflow: hidden;
  }

  .van-sidebar-item--select, .van-sidebar-item--select:active, .van-sidebar-item {
    background: #f2f2f2;
    text-align: center;
  }

  .van-sidebar-item__text {
    font-size: 14px;
    color: #333;
  }

  .van-sidebar-item--select {
    &:before {
      display: none;
    }
    .van-sidebar-item__text {
      color: $primary-color;
      font-size: 17px;
      font-weight: 600;
    }
  }

  .van-tree-select__nav-item {
    &:first-child {
      padding-top: 0;
    }
  }
}

.content {
  width: 259px;
  height: 100%;
  .subClassify {
    flex: 1;
    .tabspan {
      width: calc(100% - 32px);
      overflow-x: auto;
      font-size: 12px;
      color: #333333;
      .tab-text {
        padding: 0 9px;
        line-height: 26px;
        height: 26px;
        background: #ffffff;
        border-radius: 42px;
        text-wrap: nowrap;
        &:first-child {
          margin-left: 0;
        }
      }

    }
  }
}
.classifyBox {
  background: #fff;
  padding: 12px 22px 12px;
  .goods__list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: 8px;
    grid-row-gap: 10px;
    .goods__item {
      .name {
        height: 34px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        color: #6C6C6C;
        background-color: #F2F2F2;
        border-radius: 5px;
        padding: 0 10px;
        text-align: center;
      }
    }
  }
}
</style>
