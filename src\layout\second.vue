<template>
  <div class="layout">
    <div class="page-container">
      <router-view v-slot="{ Component }">
        <keep-alive include="HomeSearch,ClassifyFilter,CollectionZone">
          <component :is="Component" />
        </keep-alive>
      </router-view>
    </div>
  </div>
</template>

<script setup lang="ts">
</script>

<style scoped lang="scss">
.layout {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 100%;
  flex: 1 1 100%;
  position: relative;

  .page-container {
    z-index: 2;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-flow: column;
    background-color: #F2F2F2;
    overflow-y: auto;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  :deep(.van-tabbar-item) {
    font-size: 12px;
  }
}
</style>
