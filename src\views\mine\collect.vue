<template>
  <div class="pb-65">
    <NavBar v-model:manager="manager" @onSearch="onSearch" @onCancel="onCancel" :manageFlag="manageFlag">
      <template #title>
        我的收藏
      </template>
    </NavBar>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        loading-text="加载中..."
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        :immediate-check="false"
      >
        <div class="card rounded-10 bg-white pb-15 mt-10 mx-10" v-for="(item, index) in list" :key="index">
          <div class="flex items-center py-11 pl-8 pr-10">
            <img v-if="manager" :src="getSrc(item.checked as boolean)" class="size-18" @click="checkAll([item], item.checked)" />
            <span class="ml-8 text-black font-600 text-14">{{ item.groupName }}</span>
          </div>
          <ul>
            <li v-for="(child, idx) in item.children" :key="idx">
              <van-swipe-cell>
                <div class="flex items-center py-15 pl-8 pr-10" @click="toDetail(child)">
                  <img v-if="manager" :src="getSrc(child.checked as boolean)" class="size-18" @click.stop="clickItem(item, child)" />
                  <img :src="child.picUrl" class="size-94 rounded-5 ml-8" />
                  <div class="ml-10 flex flex-col">
                    <div class="font-600 text-14 text-black ellipsis-2">
                      {{ child.name }}
                    </div>
                    <div class="mt-22 mb-12 text-12 lh-12 color-#666" v-if="false">
                      收藏：{{ child.collect }}
                    </div>
                    <!-- <div class="flex items-center justify-between">
                      <span class="amount"><span class="unit">¥</span>{{ child.price | formatMoney }}</span>
                    </div> -->
                  </div>
                </div>
                <template #right>
                  <van-button class="right-btn" square type="danger" @click.stop="delItem(child.skuId)" text="删除" />
                </template>
              </van-swipe-cell>
            </li>
          </ul>
        </div>
      </van-list>
    </van-pull-refresh>
    <div class="bottom flex items-center justify-between pl-17 pr-10" v-if="manager">
      <div class="flex items-center" @click="checkAll(list, allChecked)">
        <img :src="getSrc(allChecked as boolean)" class="size-18" />
        <span class="ml-5">全选</span>
      </div>
      <div class="flex items-center">
        <van-button plain round size="small" class="ml-10 w-77 btn-primary" @click="deleteCollect">删除</van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { showToast } from 'vant'
import { computed,ref } from 'vue'

import { batchCancel,cancelCollect, getCollectPage } from '@/api/my'
import checked from '@/assets/shoppingCart/checked.png'
import unchecked from '@/assets/shoppingCart/unchecked.png'
import NavBar from '@/components/navbar/searchBar.vue'
import router from '@/router'
import { formatMoney } from '@/utils/base'

const manager = ref(false)
const manageFlag = ref(true)
const searchVal = ref('')

const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)

const onLoad = () => {
  getCollectPageHandler()
}

const onRefresh = () => {
  // 清空列表数据
  finished.value = false

  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true
  reset()
  onLoad()
}

const reset = () => {
  pageIndex.value = 1
  list.value = []
  searchVal.value = ''
}

const pageSize = ref(20)
const pageIndex = ref(1)
const list = ref([] as any)
const checkList = ref([] as any)
const getCollectPageHandler = async () => {
  if (refreshing.value) {
    refreshing.value = false
  }
  loading.value = true
  const params = {
    pageNo: pageIndex.value,
    pageSize: pageSize.value,
  }
  if (searchVal.value) {
    params.keyword = searchVal.value
  }
  const data = await getCollectPage(params)
  loading.value = false
  if (data && data.list) {
    data.list.forEach(item => {
      const index = list.value.findIndex(x => x.id === item.supplierId)
      if (index === -1) {
        list.value.push({
          groupName: item.supplierName,
          id: item.supplierId,
          checked: false,
          children: [
            { picUrl: item.picUrl, name: item.skuName, collect: '999+', checked: false, skuId: item.skuId, supplierId: item.supplierId }
          ]
        })
      } else {
        list.value[index].children.push({
          picUrl: item.picUrl, name: item.skuName, collect: '999+', checked: false, skuId: item.skuId, supplierId: item.supplierId
        })
      }
    })
    pageIndex.value++
  }
  if (list.value.length >= data.total) {
    finished.value = true
  }
}

const getSrc = (haschecked: boolean): string => (haschecked ? checked : unchecked)

const onSearch = (val: string) => {
  reset()
  searchVal.value = val
  getCollectPageHandler()
}

const onCancel = () => {
  reset()
  getCollectPageHandler()
}

const allChecked = computed(() => {
  let flag = true
  list.value.forEach((item) => {
    if (!item.checked) {
      flag = false
    }
    item.children.forEach((foo) => {
      if (!foo.checked) {
        flag = false
      }
    })
  })
  return flag
})

const checkAll = (arr: any, checkFlag: boolean) => {
  const flag = !checkFlag
  arr.forEach((item: any) => {
    item.checked = flag
    item.children.forEach((foo: any) => {
      foo.checked = flag
      const i = checkList.value.findIndex(x => x.skuId === foo.skuId)
      if (flag) {
        if (i === -1) {
          checkList.value.push(foo)
        }
      } else {
        checkList.value.splice(i, 1)
      }
    })
  })
}

const clickItem = (item: any, child: any) => {
  child.checked = !child.checked
  let flag = true
  item.children.forEach((data) => {
    if (!data.checked) {
      flag = false
    }
  })
  item.checked = flag
  const i = checkList.value.findIndex(x => x.skuId === child.skuId)
  if (child.checked) {
    if (i === -1) {
      checkList.value.push(child)
    }
  } else {
    checkList.value.splice(i, 1)
  }
}

const deleteCollect = async () => {
  if (checkList.value.length === 0) {
    showToast('请选择需要删除的收藏商品')
    return
  }
  await batchCancel(checkList.value.map(x => x.skuId))
  showToast('删除收藏成功')
  reset()
  getCollectPageHandler()
}

// 单独删除收藏
const delItem = async (skuId: string) => {
  await cancelCollect({ skuId })
  showToast('删除收藏成功')
  reset()
  getCollectPageHandler()
}

const toDetail = (data: any) => {
  router.push({
    path: '/goods/detail',
    query: {
      supplierId: data.supplierId,
      skuId: data.skuId
    }
  })
}

getCollectPageHandler()
</script>

<style scoped lang="scss">
.amount {
  color: $primary-color;
  font-size: 17px;
  font-weight: 600;
  .unit {
    font-size: 12px;
  }
}

:deep(.van-swipe-cell__right) {
  right: -1px;
  .right-btn {
    height: 100%;
  }
}

.bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 58px;
  background-color: #fff;
}
</style>
