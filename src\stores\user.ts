import { defineStore } from 'pinia'
import { ref } from 'vue'

import { addressDefault, AppAddressRespVO } from '@/api/address'
import { loginSmsCode,loginUser } from '@/api/login'
import { getCartCount } from '@/api/shoppingCart'
import { getUserInfo } from '@/api/user'
import { setToken } from '@/utils/auth'

export const useUserStore = defineStore('user', () => {
  const userInfo = ref({
    nickname: "",
    name: "",
    avatar: "",
    mobile: "",
    userNo: "",
    deptCode: "",
    deptName: "",
    jobTitle: null
  })

  const shoppingCart = ref({
    count: 0
  })

  const defaultAddress: AppAddressRespVO = ref({
    cityId: 1381,
    cityName: '',
    countyId: 50718,
    countyName: '',
    provinceId: 17,
    provinceName: '',
    townId: null,
    townName: '',
    consigneeAddress: '',
    consigneeZip: '',
    createTime: '',
    email: '',
    id: '',
    mobile: '',
    name: ''
  })

  const userLogin = async (data: any) => {
    const result = await loginUser(data)
    setToken(result)
  }

  const smsCodeLogin = async (data: any) => {
    const result = await loginSmsCode(data)
    setToken(result)
  }

  const loadUserInfo = async () => {
    const result = await getUserInfo({})
    window.sessionStorage.setItem('session-userinfo', JSON.stringify(result))
    userInfo.value = result
  }

  const clean4Logout = () => {
    userInfo.value = null
  }

  const loadUserAddress = async () => {
    const data = await addressDefault()
    defaultAddress.value = data
  }

  const setDefaultAddress = (data: any) => {
    defaultAddress.value = data
  }

  const setCartCount = (data: any) => {
    shoppingCart.value.count = data
  }

  const loadCartCount = async () => {
    const data = await getCartCount()
    setCartCount(data)
  }

  return { userInfo, defaultAddress, shoppingCart, loadUserInfo, clean4Logout, userLogin, smsCodeLogin, setDefaultAddress, loadUserAddress, loadCartCount }
})

