import request from '@/utils/request'

const requestS = (data: any) =>
  request({
    ...data,
    ...{
      baseURL: import.meta.env.VITE_BASE_SYSTEM_API
    }
  })

// 公告列表
export function contentList(params: any) {
  return requestS({
    url: '/cms/content/list',
    method: 'get',
    params
  })
}

// 公告详情
export function contentDetail(id: any) {
  return requestS({
    url: `/cms/content/info-by-id/${id}`,
    method: 'get'
  })
}
