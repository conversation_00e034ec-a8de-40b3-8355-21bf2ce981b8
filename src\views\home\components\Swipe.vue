<template>
  <van-swipe :autoplay="3000" lazy-render class="h-195">
    <van-swipe-item v-for="image in images" :key="image">
      <img :src="image.imgUrl" class="h-full w-355 rounded-10" @click="handleClick(image)" />
    </van-swipe-item>
  </van-swipe>
</template>

<script setup lang="ts">
defineOptions({
  name: 'Swipe'
})

import { ref } from 'vue'
import { useRouter } from 'vue-router'

import { getAdvPositionList } from '@/api/home'

const images = ref([])
const router = useRouter()

const getImgList = async () => {
  const res = await getAdvPositionList({
    type: 10,
    terminalType: 2,
    bizType: 1
  })
  images.value = res as any || []
}

const handleClick = (image:any) => {
  if(image.link) {
    if(image.link.indexOf('http') >= 0) {
      window.open(image.link, '_blank')
    } else {
      router.push(image.link)
    }
  }
}

getImgList()
</script>

<style scoped lang="scss"></style>
