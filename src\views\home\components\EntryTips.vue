<template>
  <div></div>

  <van-dialog v-model:show="showDialog" title="温馨提示" confirmButtonText="我知道了" @confirm="confirmed">
    <div class="tip-content" v-html="configData.globalTipContent"></div>
  </van-dialog>
</template>

<script setup lang="ts">
defineOptions({
  name: 'EntryTips'
})
import { ref } from 'vue'

import { useHomeStore } from '@/stores/home';

const showDialog = ref(false)
const homeStore = useHomeStore()
const configData = homeStore.configInfo.configData || {}
const ckey = 'jcy-entry-tips-indicator'

const confirmed = () => {
  sessionStorage.setItem(ckey, '1')
}

const handle = () => {
  const value = sessionStorage.getItem(ckey)
  const needShow = configData.globalTipSwitch && configData.globalTipContent && !value
  if(needShow) {
    showDialog.value = true
  }
}

handle()
</script>

<style scoped lang="scss">
.tip-content {
  padding: 10px 20px;
}
</style>
