<template>
  <div>
    <van-sticky>
      <NavBar title="全部订单" :clickLeft="goBack" />
    </van-sticky>
    <van-tabs
      v-model:active="active"
      background="#F2F2F2"
      line-height="0"
      sticky
      offset-top="46px"
    >
      <van-tab v-for="(item, index) in tabList" :key="index" :title="item.label" :name="item.value">
        <OGoodsList v-if="active === item.value" :orderType="active" style="height: calc(100vh - 82px); overflow: auto" />
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'

defineOptions({
  name: 'OrderIndex'
})
import {onMounted, ref} from 'vue'

import NavBar from '@/components/navbar/index.vue'
import { ORDER_STATUS_LIST } from '@/utils/orderUtils'
import OGoodsList from '@/views/order/components/oGoodsList.vue'

const active = ref()
const route = useRoute()
const router = useRouter()

const goBack = () => {
  router.push('/mine')
}

const tabList = [
  {
    label: '全部',
    value: ''
  }
].concat(ORDER_STATUS_LIST.filter(item => ![4,5,7].includes(item.value)))

onMounted(() => {
  if (route.query.status) {
    active.value = Number(route.query.status)
  }
})
</script>

<style scoped lang="scss">
:deep(.van-tabs) {
  .van-tabs__wrap {
    height: 36px;
  }
  .van-tab {
    line-height: 17px;
  }
}
</style>
