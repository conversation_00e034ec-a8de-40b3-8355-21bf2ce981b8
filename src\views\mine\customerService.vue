<template>
  <div class="position-relative pl-10 pr-10">
    <NavBar title="客户服务"></NavBar>
    <div class="service-item bg-white p-10 mt-10 mb-10 h-55 flex items-center justify-between" v-for="item in orderList">
      <div class="flex items-center">
        <img class="size-35 rounded-6 mr-5" :src="item.logoUrl" alt="">
        <div>
          <div class="lh-17 font-600 text-14 text-black">{{ item.fullName }}</div>
          <div class="lh-17 text-14 color-#999">{{ item.serviceAgent }} {{ item.servicePhone }}</div>
        </div>
      </div>
      <div @click="callPhone(item.servicePhone)" class="bg-#F2F2F2 w-48 h-24 lh-24 rounded-12 text-center color-#999">呼叫</div>
    </div>
    <div class="service-item bg-white p-10 mt-10 mb-10 h-55 flex items-center justify-between">
      <div class="flex items-center">
        <!-- <img class="size-35 rounded-6 mr-5"  alt=""> -->
        <div>
          <div class="lh-17 font-600 text-14 text-black">平台技术运营</div>
          <div class="lh-17 text-14 color-#999">183 0272 0022</div>
        </div>
      </div>
      <div @click="callPhone('18302720022')" class="bg-#F2F2F2 w-48 h-24 lh-24 rounded-12 text-center color-#999">呼叫</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

import { getServiceList } from '@/api/my'
import NavBar from '@/components/navbar/index.vue'

const orderList = ref<any>([])

const callPhone = (phone: string) => {
  window.location.href = `tel:${ phone }`
}

const getServiceListHandler = async () => {
  const data = await getServiceList({
    pageNo: 1,
    pageSize: 25
  })
  orderList.value = data
}
getServiceListHandler()
</script>

<style scoped lang="scss">
</style>
