<template>
  <div class="rounded-10 px-15 py-20 bg-white history">
    <div class="flex items-center justify-between" v-if="list && list.length">
      <div class="font-600 text-black lh-16">历史搜索</div>
      <img @click="delHistory" :src="del" class="size-14"/>
    </div>

    <div class="flex items-center flex-wrap mt-10">
      <div
        class="bg-[#F2F2F2] rounded-12 px-9 py-4 mr-5 mb-5 text-12 text-[#999999] lh-16"
        v-for="(item, index) in list"
        :key="index"
        @click="search(item.keyword)"
      >
        {{ item.keyword }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { showToast } from 'vant'
import { ref } from 'vue'

import { cleanHistory,getHistory } from '@/api/classify'

defineOptions({
  name: 'History'
})
import del from '@/assets/goods/del.png'

const list = ref([])

const emits = defineEmits(['search'])

const search = (name: string) => {
  emits('search', name)
}

const delHistory = async () => {
  list.value = []
  await cleanHistory()
  showToast('历史记录删除成功')
}

const getHistoryList = async () => {
  const data = await getHistory()
  list.value = data || []
}
getHistoryList()
</script>

<style scoped lang="scss">
.history {
  min-height: calc(100vh - 50px);
}
</style>
