import { defineConfig, presetUno } from 'unocss'
import presetRemToPx from '@unocss/preset-rem-to-px'

export default defineConfig({
  rules: [
    [/^line-height-(\d+(?:\.\d+)?)$/, ([, d]) => ({ 'line-height': d })],
    [/^font-size-(\d+)$/, ([, d]) => ({ 'font-size': `${Number(d)}px` })],
    ['flex-center', { display: 'flex', 'align-items': 'center', 'justify-content': 'center' }],
    [
      /^ellipsis-(\d+)$/,
      ([, d]) => ({
        overflow: 'hidden',
        'text-overflow': 'ellipsis',
        display: '-webkit-box',
        '-webkit-line-clamp': d,
        '-webkit-box-orient': 'vertical'
      })
    ]
  ],
  presets: [
    presetUno(),
    presetRemToPx({
      baseFontSize: 4
    })
  ]
})
