<template>
  <div class="flex flex-col group-filter bg-white py-10 rounded-10">
    <div class="flex items-center justify-between">
      <div v-for="(item, index) in data" :key="index" class="flex-1 flex-center" @click="selectType(item, index)">
        <div class="font-500 text-12 text-[#666666]" :class="{ active: selectIndex === index }">
          {{ item.name }}
        </div>
        <div v-if="item.hasSort">
          <Sort :descending="item.descending" :ascending="item.ascending" @change="selectType(item, index)" />
        </div>
        <div v-if="item.hasFilter" class="ml-5">
          <img :src="arrowDown" class="w-10 h-6" />
        </div>
      </div>
    </div>
    <div v-if="openFilter" class="px-15 py-10 pb-15">
      <div class="filter-wrap">
        <div v-for="(item, index) in filterData" :key="index">
          <div class="flex flex-col mt-10" v-if="item.list.length > 0">
            <div class="font-600 text-black mb-10">{{ item.name }}</div>
            <div class="flex flex-wrap">
              <span
                class="px-10 py-8 mr-10 mb-10 lh-14 text-black rounded-5 classifyName"
                v-for="(child, idx) in item.list"
                :key="child.id + idx"
                :class="{ classifyActive: child.checked }"
                @click="checkItem(index, child)"
                >{{ child.name }}</span
              >
            </div>
          </div>
        </div>
        <div>
          <div class="flex flex-col mt-10">
            <div class="font-600 text-black mb-10">价格区间（元）</div>
            <div class="flex items-center">
              <van-stepper
                :show-minus="false"
                :show-plus="false"
                placeholder="自定最低价"
                v-model="data[2].min"
                input-width="98px"
                min="0"
              />
              <span class="mx-7 text-[#D9D9D9]">-</span>
              <van-stepper
                :show-minus="false"
                :show-plus="false"
                placeholder="自定最高价"
                v-model="data[2].max"
                input-width="98px"
                :min="0"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="px-52 mt-15 flex justify-between gap-12">
        <van-button round block class="btn-primary" @click="complete">完成</van-button>
        <van-button round block @click="resetFilter">重置</van-button>
      </div>
    </div>
  </div>
  <van-overlay
    :show="openFilter"
    @click="openFilter = false"
    :duration="0"
    z-index="90"
    class="h-[90%] mt-80 rounded-10"
  />
</template>

<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'

defineOptions({
  name: 'Group'
})

const props = defineProps({
  listLength: {
    type: Number,
    default: 0
  },
  filterItems: {
    type: Object,
    default: {
      supplierAggList: [],
      categoryAggList: [],
      brandAggList: []
    }
  },
  filterParams: {
    type: Object
  }
})

import arrowDown from '@/assets/sort/arrow-down.png'
import Sort from '@/components/sort/sort.vue'

const emits = defineEmits(['change'])

const data = reactive([
  // { name: '综合', type: '', hasSort: false, hasFilter: false },
  { name: '销量降序', type: 1, hasSort: false, hasFilter: false },
  { name: '价格', type: 2, hasSort: true, hasFilter: false, ascending: false, descending: false },
  { name: '筛选', hasSort: false, hasFilter: true, max: 0, min: 0, supplierId: '', categoryId1: '', categoryId2: '', categoryId3: '', brandId: '' }
])

const filterData:any = ref([])

const selectIndex = ref(0)
const openFilter = ref(false)
const checkData = ref(data[0])

const route = useRoute()
const query = route.query
watch(() => props.filterItems, () => {
  initData(props.filterParams, 'watch')
}, { deep: true })

const initData = (params: any, type: string) => {
  props.filterItems.supplierAggList = props.filterItems.supplierAggList || []
  props.filterItems.categoryAggList = props.filterItems.categoryAggList || []
  props.filterItems.brandAggList = props.filterItems.brandAggList || []
  const checkCategory = params.categoryId1 || params.categoryId2 || params.categoryId3 || ''
  let categoryList = []
  const { categoryId1, categoryId2, categoryId3, supplierId, brandId } = params
  if (type === 'watch') {
    selectIndex.value = 0
    data[1].ascending = false
    data[1].descending = false
    data[2].max = 0
    data[2].min = 0
    if (categoryId1) {
      categoryList = props.filterItems.categoryAggList.filter(x => x.cateLevel === 2) || []
    } else if (categoryId2) {
      categoryList = props.filterItems.categoryAggList.filter(x => x.cateLevel === 3) || []
    } else if (categoryId3) {
      categoryList = []
    } else {
      categoryList = props.filterItems.categoryAggList.filter(x => x.cateLevel === 1) || []
    }
  } else if (type === 'mounted') {
    if (params.sortType === 1) {
      selectIndex.value = 0
      data[1].ascending = false
      data[1].descending = false
      checkData.value = data[0]
    } else if (params.sortType === 2) {
      selectIndex.value = 1
      data[1].ascending = true
      data[1].descending = false
      checkData.value = data[1]
    } else if (params.sortType === 3) {
      selectIndex.value = 1
      data[1].ascending = false
      data[1].descending = true
      checkData.value = data[1]
    }
    data[2] = {
      name: '筛选',
      hasSort: false,
      hasFilter: true,
      max: params.maxPrice || 0,
      min: params.minPrice || 0,
      supplierId,
      categoryId1,
      categoryId2,
      categoryId3,
      brandId
    }
    // 如果是回退展示，有categoryId1时表示展示的分类也是1级类目，这样才能选中
    if (categoryId1) {
      categoryList = props.filterItems.categoryAggList.filter(x => x.cateLevel === 1) || []
    } else if (categoryId2) {
      categoryList = props.filterItems.categoryAggList.filter(x => x.cateLevel === 2) || []
    } else if (categoryId3) {
      categoryList = props.filterItems.categoryAggList.filter(x => x.cateLevel === 3) || []
    } else {
      categoryList = []
    }
  }
  filterData.value = [
    {
      name: '电商筛选',
      list: props.filterItems.supplierAggList.map(x => ({
        name: x.name,
        id: x.supplierId,
        checked: supplierId === x.supplierId
      }))
    },
    {
      name: '分类筛选',
      list: categoryList.map(x => ({
        name: x.cateName,
        id: x.categoryId,
        cateLevel: x.cateLevel,
        checked: checkCategory === x.categoryId
      }))
    },
    {
      name: '品牌筛选',
      list: props.filterItems.brandAggList.map(x => ({
        name: x.name,
        id: x.brandId,
        checked: brandId === x.brandId
      }))
    }
  ]
}

defineExpose({
  initData
})

watch(openFilter, () => {
  const setOverFlowHidden = document.querySelector('.setOverFlowHidden')
  if (setOverFlowHidden) {
    if (openFilter.value) {
      setOverFlowHidden.style.overflow = 'hidden'
    } else {
      setOverFlowHidden.style.overflow = 'initial'
    }
  }
})

const complete = () => {
  openFilter.value = !openFilter.value
  if (data[2].max < data[2].min) {
    [data[2].min, data[2].max] = [data[2].max, data[2].min]
  }
  emits('change', checkData.value, data[2])
  console.log('data2---', data[2])
}

const resetFilter = () => {
  data[2].min = 0
  data[2].max = 0
  data[2].supplierId = query.supplierId || ''
  data[2].categoryId1 = query.categoryId1 || ''
  data[2].categoryId2 = query.categoryId3 || ''
  data[2].categoryId3 = query.categoryId3 || ''
  data[2].brandId = ''
  filterData.value.forEach(item => {
    if(item.name === '电商筛选' && query.supplierId) {
      return
    }
    if(item.name === '分类筛选' && (query.categoryId1 || query.categoryId2 || query.categoryId3)) {
      return
    }
    item.list.forEach(x => {
      x.checked = false
    })
  })
}

const selectType = (item: any, index: number) => {
  if (index !== 2) {
    selectIndex.value = index
  }
  const changeSort = (changeItem: any) => {
    if (changeItem.ascending) {
      changeItem.ascending = false
      changeItem.descending = true
    } else if (changeItem.descending) {
      changeItem.ascending = true
      changeItem.descending = false
    } else {
      changeItem.ascending = true
      changeItem.descending = false
    }
  }
  if (index === 2) {
    openFilter.value = !openFilter.value
  } else {
    if (index === 0) {
      checkData.value = item
      data[1].ascending = false
      data[1].descending = false
    } else if (index === 1) {
      changeSort(item)
      checkData.value = item
    }
    emits('change', item, data[2])
  }
}

const checkItem = (index: number, item: any) => {
  if(index === 0 && query.supplierId) {
    return
  }
  if(index === 1 && (query.categoryId1 || query.categoryId2 || query.categoryId3)) {
    // return
  }
  const newChecked = !item.checked
  filterData.value[index].list.forEach(x => {
    if(x.id === item.id) {
      x.checked = newChecked
    } else {
      x.checked = false
    }
  })
  const val = newChecked ? item.id : ''
  if (index === 0) {
    data[2].supplierId = val
  } else if (index === 1) {
    data[2].categoryId1 = ''
    data[2].categoryId2 = ''
    data[2].categoryId3 = ''
    data[2][`categoryId${ item.cateLevel }`] = val
  } else if (index === 2) {
    data[2].brandId = val
  }
}
</script>

<style scoped lang="scss">
.active {
  color: #000;
  font-weight: 600;
}

.group-filter {
  position: relative;
  z-index: 101;
}

.classifyName {
  border: 1px solid #000000;
  &.classifyActive {
    border: 1px solid $primary-color;
    color: $primary-color;
  }
}

:deep(.van-stepper__input) {
  height: 38px;
  border-radius: 5px;
}
.filter-wrap {
  max-height: 400px;
  overflow-y: auto;
}
</style>
