<template>
  <div class="rounded-10 bg-white h-100vh overflow-y-auto" ref="scrollContainer">
    <van-sticky :offset-top="46">
      <Group ref="groupRef" @change="changeList" :listLength="list.length" :filterItems="filterItems" :filterParams="filterParams" />
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-if="list.length"
        v-model:loading="loading"
        loading-text="加载中..."
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        :immediate-check="false"
      >
        <div class="flex py-10 px-10" :class="{'pt-0': idx === 0}" v-for="(child, idx) in list" :key="idx" @click="toDetail(child)">
          <div class="size-117 position-relative">
            <img :src="child.imageUrl" class="size-117 rounded-5" />
            <img v-if="isOutStock(child.stockStateType)" :src="outStock" class="out-stock size-72">
          </div>
          <div class="ml-10 flex flex-col py-5 flex-1">
            <div class="font-600 text-14 text-black ellipsis-2 lh-16">
              {{ child.skuName }}
            </div>
            <div v-if="child.salePrice != -1" class="flex items-end mt-30 font-600 text-20 color-primary">
              <div>
                <span class="unit text-12 lh-12">¥</span>
                <span class="lh-18">{{ formatMoney(child.salePrice) }}</span>
              </div>
              <div class="ml-10 m-price" v-if="enableMarketPrice()">
                <span class="unit text-12 lh-12">¥</span>
                <span class="lh-18">{{ formatMoney(child.marketPrice) }}</span>
              </div>
            </div>
            <div v-else class="flex items-end mt-30 font-500 text-12 color-#989898">
              <span class="lh-18">登录后查看价格</span>
            </div>
            <div class="flex items-center justify-between mt-14 lh-12">
              <div class="text-12 text-[#666666]" v-if="enableSupName()">{{child.supplierName}}</div>
              <!-- <div class="text-12 text-[#666666]">销量：999+</div> -->
              <div></div>
              <div class="flex items-center py-5 pl-10 h-26" @click.stop="addCart(child)">
                <van-icon name="cart-o" :color="(!isStockValid(child.stockStateType) || child.skuState != 1) ? '#999999' : '#F22E00'" class="text-16 mr-7" />
                <span class="text-12 color-primary" :class="{'c-999': !isStockValid(child.stockStateType) || child.skuState != 1}">加入购物车</span>
              </div>
            </div>
          </div>
        </div>
      </van-list>
      <EmptyBox v-else />
    </van-pull-refresh>
    <img class="size-36 shopcart-custom" @click="toShopcart" :src="shopcart" />
    <van-back-top class="goods-custom">
      <img class="size-36" :src="backTop" />
    </van-back-top>
  </div>
</template>

<script setup lang="ts">
import { closeToast,showLoadingToast, showToast } from 'vant'
import { nextTick, onActivated,onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute,useRouter } from 'vue-router'

import { goodsSearchPageList } from '@/api/classify'
import { addCartApi } from '@/api/shoppingCart'
import backTop from '@/assets/goods/backTop.png'
import outStock from '@/assets/goods/outStock.png'
import shopcart from '@/assets/goods/shopcart.png'
import EmptyBox from '@/components/emptyBox/index.vue'
import Group from '@/components/sort/group.vue'
import { useUserStore } from '@/stores/user'
import { formatMoney } from '@/utils/base'
import { enableMarketPrice, enableSupName, isOutStock,isStockValid } from '@/utils/productUtils'

defineOptions({
  name: 'Goods'
})

const props = defineProps({
  searchVal: {
    type: String,
    default: ''
  }
})

const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const route = useRoute()
const pageSize = ref(20)
const pageIndex = ref(1)
const userStore = useUserStore()
const address = userStore.defaultAddress

const filterItems = ref({
  supplierAggList: [],
  categoryAggList: [],
  brandAggList: []
})
const list = ref([])
const filterParams = ref({
  sortType: 1,
  supplierId: route.query.supplierId || '',
  categoryId1: route.query.categoryId1 || '',
  categoryId2: route.query.categoryId2 || '',
  categoryId3: route.query.categoryId3 || '',
  brandId: route.query.brandId || '',
  minPrice: 0,
  maxPrice: 0
})
const searchType = ref('init')

const onLoad = () => {
  pageIndex.value++
  getData(filterParams.value, searchType.value)
}

const onRefresh = () => {
  pageIndex.value = 1
  // 清空列表数据
  finished.value = false

  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true
  getData(filterParams.value, searchType.value)
}

const getData = async (dataParams: {
  sortType: number,
  supplierId: string,
  categoryId1: string,
  categoryId2: string,
  categoryId3: string,
  brandId: string,
  minPrice: number,
  maxPrice: number
} = {
  sortType: 1,
  supplierId: '',
  categoryId1: '',
  categoryId2: '',
  categoryId3: '',
  brandId: '',
  minPrice: 0,
  maxPrice: 0
}, flag: string) => {
  loading.value = true
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0
  });
  let areaIdArr = [address.provinceId, address.cityId, address.countyId, address.townId]
  areaIdArr = areaIdArr.filter(aid => aid !== null && aid !== undefined)
  const areaIds = areaIdArr.join(',')
  const { categoryId1, categoryId2, categoryId3, supplierId, tagIds } = route.query
  const params = {
    areaIds,
    keyword: props.searchVal,
    pageIndex: pageIndex.value,
    pageSize: pageSize.value,
    minPrice: dataParams.minPrice || '',
    sortType: dataParams.sortType
  }
  if(dataParams.maxPrice && dataParams.maxPrice > 0) {
    params.maxPrice = dataParams.minPrice
  }
  if (dataParams.supplierId || supplierId) {
    params.supplierId = dataParams.supplierId || supplierId
  }
  if (dataParams.categoryId1 || categoryId1) {
    params.categoryId1 = dataParams.categoryId1 || categoryId1
  }
  if (dataParams.categoryId2 || categoryId2) {
    params.categoryId2 = dataParams.categoryId2 || categoryId2
  }
  if (dataParams.categoryId3 || categoryId3) {
    params.categoryId3 = dataParams.categoryId3 || categoryId3
  }
  if (dataParams.brandId) {
    params.brandId = dataParams.brandId
  }
  if (tagIds) {
    params.tagIds = tagIds
  }
  const data = await goodsSearchPageList(params)
  if (refreshing.value) {
    list.value = []
    refreshing.value = false
  }
  loading.value = false
  // 如果flag是init，说明改变了关键字或初次加载，要赋值filterItems，如果是chagneFilter，那么保留之前的filterItems
  searchType.value = flag
  if (flag !== 'changeFilter') {
    filterItems.value = data
  }
  if (data.pageResult && data.pageResult.list) {
    list.value = list.value.concat(data.pageResult.list)
  } else {
    list.value = []
  }
  finished.value = list.value.length >= data.pageResult.total
  closeToast()
}

// 从详情页回显要记录当时数据
// if (sessionStorage.getItem('saveFilter')) {
//   filterItems.value = JSON.parse(sessionStorage.getItem('saveFilter'))
//   sessionStorage.removeItem('saveFilter')
//   searchType.value = 'changeFilter'
// }
// if (sessionStorage.getItem('saveParams')) {
//   filterParams.value = JSON.parse(sessionStorage.getItem('saveParams'))
//   sessionStorage.removeItem('saveParams')
// }

const router = useRouter()
const toDetail = (data: any) => {
  // sessionStorage.setItem('saveFilter', JSON.stringify(filterItems.value))
  // sessionStorage.setItem('saveParams', JSON.stringify(filterParams.value))
  router.push({
    path: '/goods/detail',
    query: {
      supplierId: data.supplierId,
      skuId: data.skuId
    }
  })
}

const changeList = (sort: any, data: any) => {
  reset()
  const params = {
    sortType: 1,
    supplierId: '',
    categoryId1: '',
    categoryId2: '',
    categoryId3: '',
    brandId: '',
    minPrice: 0,
    maxPrice: 0
  }
  if (sort.type === 1) {
    params.sortType = 1
  } else {
    params.sortType = sort.ascending ? 2 : 3
  }
  if (data.supplierId) {
    params.supplierId = data.supplierId
  }
  if (data.categoryId1) {
    params.categoryId1 = data.categoryId1
  }
  if (data.categoryId2) {
    params.categoryId2 = data.categoryId2
  }
  if (data.categoryId3) {
    params.categoryId3 = data.categoryId3
  }
  if (data.brandId) {
    params.brandId = data.brandId
  }
  if (data.min) {
    params.minPrice = data.min
  }
  if (data.max) {
    params.maxPrice = data.max
  }
  filterParams.value = params
  getData(params, 'changeFilter')
}

const reset = () => {
  pageIndex.value = 1
  list.value = []
}

// 加入购物车
const addCart = async (item: any) => {
  if(!item.skuId) {
    return
  }
  if (!isStockValid(item.stockStateType) || item.skuState != 1) {
    return
  }
  const params = {
    area: {
      provinceId: address.provinceId,
      cityId: address.cityId,
      countyId: address.countyId,
      townId: ''
    },
    count: 1,
    skuId: item.skuId,
    supplierId: item.supplierId
  }
  if (address.townId) {
    params.area.townId = address.townId
  }
  const data = await addCartApi(params)
  if (data) {
    showToast('加入购物车成功！')
    userStore.loadCartCount()
  }
}

const search = () => {
  nextTick(() => {
    reset()
    filterParams.value = {
      sortType: 1,
      supplierId: route.query.supplierId || '',
      categoryId1: route.query.categoryId1 || '',
      categoryId2: route.query.categoryId2 || '',
      categoryId3: route.query.categoryId3 || '',
      brandId: route.query.brandId || '',
      minPrice: 0,
      maxPrice: 0
    }
    searchType.value = 'init'
    getData(filterParams.value, searchType.value)
  })
}

const groupRef = ref()
const scrollContainer = ref(null);
const scrollPosition = ref(0);
onMounted(() => {
  console.log('onMounted----')
  if (searchType.value === 'changeFilter') {
    groupRef.value.initData(filterParams.value, 'mounted')
  }
  if (scrollContainer.value && scrollContainer.value.addEventListener) {
    scrollContainer.value.addEventListener('scroll', handleScroll);
  }
})

onUnmounted(() => {
  if (scrollContainer.value && scrollContainer.value.removeEventListener) {
    scrollContainer.value.removeEventListener('scroll', handleScroll);
  }
});

onActivated(() => {
  nextTick(() => {
    if(scrollContainer.value) {
      scrollContainer.value.scrollTop = scrollPosition.value
    }
  });
});

function handleScroll() {
  scrollPosition.value = scrollContainer.value.scrollTop;
}

const toShopcart = () => {
  router.push('/shoppingCart')
}

defineExpose( { search } )
</script>

<style scoped lang="scss">
.out-stock {
  position: absolute;
  left: 22.5px;
  top: 22.5px;
}
.c-999 {
  color: #999;
}
.m-price {
  font-size:0.7em;
  color:#989898;
  text-decoration: line-through;
}
</style>
<style lang="scss">
.goods-custom {
  box-shadow: initial;
  background-color: initial;
  width: 36px;
  height: 36px;
  right: 13px;
  bottom: 76px;
}
.shopcart-custom {
  position: absolute;
  right: 13px;
  bottom: 120px
}
</style>