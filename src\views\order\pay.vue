<template>
  <NavBar title="订单支付"></NavBar>
  <div class="flex-center flex-col position-relative">
    <van-icon name="pending-payment" class="mt-20 color-primary" size="80" />
    <div class="mt-20 text-16 font-500 text-black lh-24">支付金额：￥{{ formatMoney(orderInfo.payPrice) }}</div>
    <div class="text-12 mt-10 px-20 lh-20 text-center">
      请您尽快完成支付，超时订单会自动取消。
      <br>订单号：{{ orderInfo.no }}
    </div>

    <div class="p-16 w-full lh-26">
      <div class="p-10 tip rounded-6 bg-[#fff]">
        <div class="text-[red]">重要说明：</div>
        <div>1、商城支付平台目仅支付下方支付方式。</div>
        <div>2、其它支付渠道正在调试中，敬请期待。</div>
      </div>
    </div>

    <div class="w-full p-16 pt-0">
      <div class="bg-[#fff] rounded-6 p-10">
        <div class="font-600">支付平台</div>
        <van-radio-group v-model="payChannel">
          <van-cell-group inset>
            <van-cell v-for="ch in channelList" :key="ch" clickable @click="payChannel = ch">
              <template #icon>
                <div class="van-cell__title">
                  <img :src="getChannelImg(ch)" class="h-20" />
                </div>
              </template>
              <template #right-icon>
                <van-radio :name="ch" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </div>

    <div class="mt-40 flex flex-col items-center" v-if="payValid">
      <van-button round class="w-180 btn-primary" @click="submitPay">立即支付</van-button>
    </div>

    <PayBridge ref="payBridge"></PayBridge>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'Pay'
})
import { showFailToast, showToast } from 'vant'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { getChannelList, getDetailOrder, getPayOrder } from '@/api/order'
import NavBar from '@/components/navbar/index.vue'
import { formatMoney } from '@/utils/base'
import PayBridge from '@/views/order/payBridge.vue'

const route = useRoute()
const router = useRouter()
const payValid = ref(false)
const orderInfo = ref({})
const channelList = ref([])
const payInfo = ref({})
const payChannel = ref()
const payBridge = ref()

const getChannelImg = (channel: any) => new URL(`../../assets/order/pay_${ channel }.png`, import.meta.url).href

const orderNo = computed(() => route.query.no)

const submitPay = () => {
  if(!payChannel.value) {
    showToast('请选择支付平台')
    return
  }
  payBridge.value.submitPayment(payInfo.value.payOrderId, payChannel.value)
}

const validatePayable = () => {
  if (![5, 7].includes(orderInfo.value.paymentMethod)) {
    showFailToast('订单不支持此支付方式')
    return false
  }
  if (orderInfo.value.payed) {
    showFailToast('订单已经支付完成，无法重复支付')
    return false
  }
  if (!orderInfo.value.payPrice) {
    showFailToast('订单待支付金额为0，无法支付')
    return false
  }

  return true
}

const loadOrderInfo = async () => {
  if (!orderNo.value) {
    showToast('必选参数为空')
    return
  }
  const res = await getDetailOrder({ orderNo: orderNo.value })
  orderInfo.value = res
  payValid.value = validatePayable()
}

const loadChannelList = async () => {
  if(!payInfo.value.appCode) {
    showToast('必选参数为空')
    return
  }
  const data = await getChannelList({appCode: payInfo.value.appCode})
  channelList.value = data
  if(channelList.value.length > 0) {
    payChannel.value = channelList.value[0]
  }
}

const loadPayInfo = async () => {
  if (!orderNo.value) {
    showToast('必选参数为空')
    return
  }
  const res = await getPayOrder({ orderNo: orderNo.value })
  payInfo.value = res
  loadChannelList()
}

onMounted(() => {
  loadOrderInfo()
  loadPayInfo()
})
</script>

<style scoped lang="scss">
.tip {
  border: 1px solid $primary-color;
}
</style>
