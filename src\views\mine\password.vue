<template>
  <div class="position-relative">
    <NavBar title="修改密码" />
    <div class="mt-20">
      <van-form @submit="onSubmit">
        <van-cell-group inset>
          <van-field
            v-model="form.oldPassword"
            :type="inputType1"
            name="原密码"
            label="原密码"
            placeholder="请输入原密码"
            :rules="[{ required: true, message: '请填写原密码' }]"
          >
            <template #right-icon>
              <van-icon
                :name="inputType1 === 'password' ? 'closed-eye' : 'eye-o'"
                @click="inputType1 = inputType1 === 'password' ? 'text' : 'password'"
              />
            </template>
          </van-field>
          <van-field
            v-model="form.newPassword"
            :type="inputType2"
            name="新密码"
            label="新密码"
            placeholder="请输入新密码"
            :rules="[{ required: true, message: '请填写新密码' }]"
          >
            <template #right-icon>
              <van-icon
                :name="inputType2 === 'password' ? 'closed-eye' : 'eye-o'"
                @click="inputType2 = inputType2 === 'password' ? 'text' : 'password'"
              />
            </template>
          </van-field>
          <van-field
            v-model="form.newPassword2"
            :type="inputType3"
            name="确认新密码"
            label="确认新密码"
            placeholder="请再次输入新密码"
            :rules="[{ required: true, message: '请再次填写新密码' }]"
          >
            <template #right-icon>
              <van-icon
                :name="inputType3 === 'password' ? 'closed-eye' : 'eye-o'"
                @click="inputType3 = inputType3 === 'password' ? 'text' : 'password'"
              />
            </template>
          </van-field>
        </van-cell-group>
        <div class="m20">
          <van-button round block type="primary" :loading="submitLoading" native-type="submit"> 提交 </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { showFailToast,showToast } from 'vant'
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

import { changePassword } from '@/api/user'
import NavBar from '@/components/navbar/index.vue'
import { encryptSm2 } from '@/utils/auth'

const router = useRouter()
const submitLoading = ref(false)

const inputType1 = ref('password')
const inputType2 = ref('password')
const inputType3 = ref('password')

const form = reactive({
  oldPassword: '',
  newPassword: '',
  newPassword2: '',
})

const validateForm = () => {
  if (!form.oldPassword) {
    showToast('请输入原密码')
    return false
  }
  if (!form.newPassword) {
    showToast('请输入新密码')
    return false
  }
  if (!form.newPassword2) {
    showToast('请输入确认密码')
    return false
  }
  if (!/^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,20}$/.test(form.newPassword)) {
    showToast('密码要求长度8位至20位，必须包含数字、字母及特殊字符')
    return false
  }
  if (form.newPassword !== form.newPassword2) {
    showToast('两次密码不一致')
    return false
  }
  return true
}

const onSubmit = async () => {
  if(!validateForm()) {
    return
  }
  const params = {
    oldPassword: encryptSm2(form.oldPassword),
    newPassword: encryptSm2(form.newPassword)
  }
  submitLoading.value = true
  changePassword(params).then(() => {
    showToast('修改成功')
    router.go(-1)
  }).catch(() => {
    showFailToast('提交失败，请稍后重试！')
  }).finally(() => {
    submitLoading.value = false
  })
}

</script>

<style scoped lang="scss"></style>
