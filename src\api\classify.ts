import request from '@/utils/request'

export interface collectType {
  skuId: string,
  supplierId: string
}

const requestP = (data: any) => request({
  ...data,
  ...{
    baseURL: import.meta.env.VITE_BASE_PRODUCT_API
  }
})

const requestT = (data: any) => request({
  ...data,
  ...{
    baseURL: import.meta.env.VITE_BASE_TRADE_API
  }
})

// 查询父类下所有级别的分类
export function getChildCategoryTreeList(params: {
  parentCategoryId: string
}) {
  return request({
    url: '/product/category/getChildTreeList',
    method: 'get',
    params
  })
}

// 搜索商品
export function goodsSearchPageList(params: any) {
  return requestP({
    url: '/sku/goodsSearchPageList',
    method: 'get',
    params
  })
}

// 查询单个商品的详细信息，包含商品售卖单位、重量、产地、包装清单、主图、规格参数 、查询商品上架、详情图片、可售、权益等
export function getSkuDetailInfo(params: any) {
  return requestP({
    url: '/sku/getSkuDetailInfo',
    method: 'get',
    params
  })
}

// 查询京东单个商品的库存状态，价格，轮播图等
export function getSkuStockInfo(params: any) {
  return requestP({
    url: '/sku/getSkuStockInfo',
    method: 'get',
    params
  })
}

// 查询京东单个商品的预计到货时间
export function getSkuPredictPromise(params: any) {
  return requestP({
    url: '/vopgoods/getSkuPredictPromise',
    method: 'get',
    params
  })
}

// 查询商品库存状态等
export function queryGoodsStockInfo(params: any) {
  return requestP({
    url: '/vopgoods/queryGoodsStockInfo',
    method: 'get',
    params
  })
}

// 查询是否收藏
export function queryCollectStatus(data: {
  skuIds: []
}) {
  return requestT({
    url: '/collect/queryCollectStatus',
    method: 'post',
    data
  })
}

// 收藏商品
export function addToCollect(params: collectType) {
  return requestT({
    url: '/collect/addToCollect',
    method: 'post',
    params
  })
}

// 取消收藏商品
export function cancelCollect(params: collectType) {
  return requestT({
    url: '/collect/cancelCollect',
    method: 'post',
    params
  })
}

// 查询单个商品同品目销量排行榜列表
export function getSkuSuggestList(params: any) {
  return requestP({
    url: '/sku/getSkuSuggestList',
    method: 'get',
    params
  })
}

// 搜索历史
export function getHistory() {
  return requestP({
    url: '/search-history/get-list',
    method: 'get'
  })
}

// 清空历史
export function cleanHistory() {
  return requestP({
    url: '/search-history/clean',
    method: 'post'
  })
}

// 查询商品类型名称
export function getSkuCategoryName(params: any) {
  return requestP({
    url: '/category/getNamePath',
    method: 'get',
    params
  })
}