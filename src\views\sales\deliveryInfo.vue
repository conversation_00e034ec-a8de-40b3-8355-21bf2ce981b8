<template>
  <div :class="{ 'mb-64': orderItemData.supplierType !== 1 }">
    <van-sticky>
      <NavBar title="补充发货信息" class="!bg-white" />
      <div class="payType w-full h-100 px-20 py-26 flex items-center">
        <img :src="money" class="size-48" />
        <div class="flex flex-col ml-10 text-white">
          <div class="font-600 text-20 lh-20">{{ statusDics[afterSaleInfo.status] }}</div>
          <div class="font-200 text-14 lh-20 mt-10" v-if="afterSaleInfo.status === 20">请您尽快填写退回商品的物流信息</div>
          <div class="font-200 text-14 lh-20 mt-10" v-if="afterSaleInfo.status === 62">{{afterSaleInfo.auditReason}}</div>
          <div class="font-200 text-14 lh-20 mt-10" v-if="afterSaleInfo.status === 63">{{afterSaleInfo.receiveReason}}</div>
        </div>
      </div>
    </van-sticky>

    <van-form v-if="afterSaleInfo.status === 20" ref="formRef" input-align="right"
      required error-message-align="right" class="mt-20" @submit="onSubmitDelivery">
      <van-cell-group title="发货信息">
      <van-field
          v-model="deliveryForm.logisticsName" name="logisticsName" label="物流公司"  placeholder="物流公司"
          :rules="[{ required: true, message: '请填写物流公司' }]"
        />
      <van-field
        v-model="deliveryForm.logisticsNo" name="logisticsNo" label="快递单号"  placeholder="快递单号"
        :rules="[{ required: true, message: '请填写快递单号' }]"
      />
      <van-field
        v-model="deliveryForm.deliveryTime" is-link name="deliveryTime" label="退货时间"  placeholder="退货时间"
        :rules="[{ required: true, message: '请填写退货时间' }]" @click="showDatePicker = true"
      />
      </van-cell-group>
    </van-form>

    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker v-model="deliveryTime" :max-date="maxDeliveryTime" @confirm="onConfirmDate" @cancel="showDatePicker = false" />
    </van-popup>

    <div class="px-15 py-10 mt-20 w-full bottom-0 bg-[#F2F2F2]">
      <van-row gutter="20" v-if="[20].includes(afterSaleInfo.status) && orderItemData.orderStatus !== 9">
        <van-col span="12">
          <van-button class="btn-primary" round block @click="goBack"> 返回 </van-button>
        </van-col>
        <van-col span="12">
          <van-button class="btn-primary" round block @click="onSubmitDelivery"> 提交 </van-button>
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { showSuccessToast,showToast } from 'vant'
import { onMounted, reactive,ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { afterOrderItemSaleDetail, saveDelivery } from '@/api/afterSale'
import money from '@/assets/order/money.png'
import NavBar from '@/components/navbar/index.vue'

defineOptions({
  name: 'AfsDeliveryInfo'
})

const route = useRoute()
const router = useRouter()

const statusDics = {
  10: '等待卖家审核',
  20: '等待您退货',
  30: '等待卖家收货',
  40: '等待平台退款',
  50: '退款完成',
  62: '卖家拒绝了您的售后申请',
  63: '卖家拒绝收货'
}

const deliveryForm = reactive({
  logisticsName: '',
  logisticsNo: '',
  deliveryTime: '',
})

const formRef = ref()
const afterSaleInfo = ref({})
const orderItemData = ref({})
const maxDeliveryTime = new Date()
const showDatePicker = ref(false)
const now = new Date()
const deliveryTime = ref([now.getFullYear(), now.getMonth() + 1, now.getDate()])

const goBack = () => {
  router.go(-1)
}

const onConfirmDate = ({selectedValues}) => {
  deliveryForm.deliveryTime = selectedValues.join('-')
  showDatePicker.value = false
}

const onSubmitDelivery = async () => {
  await formRef.value.validate()
  console.log('submit', deliveryForm)
  const params = { ...deliveryForm}
  params.no = afterSaleInfo.value.no
  await saveDelivery(params)
  showSuccessToast('物流信息提交成功')
  router.go(-1)
}

const getAfterOrderItemSaleDetail = async () => {
  const res = await afterOrderItemSaleDetail({
    orderNo: route.query.orderNo,
    orderItemId: route.query.orderItemId
  })
  orderItemData.value = res
  orderItemData.value.orderNo = route.query.orderNo
  afterSaleInfo.value = res.afterSaleInfo || {}

  if(afterSaleInfo.value.status !== 20) {
    showToast("当前售后单无须补充发货信息")
  }
}

onMounted(() => {
  getAfterOrderItemSaleDetail()
})
</script>

<style scoped lang="scss">
.payType {
  background: url('@/assets/order/group.png') no-repeat center;
  background-size: 100% 100%;
}
</style>
