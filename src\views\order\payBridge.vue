<template>
  <div>
    <form id="submit_form" method="post" :action="serverUrl" class="position-fixed top-[400vh] left-[400vh]">
      <input type="hidden" name="json" :value="json" />
      <input type="hidden" name="signature" :value="signature" />
    </form>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'PayBridge'
})

import { showFailToast, showLoadingToast, showToast } from 'vant'
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'

import { submitPayOrder } from '@/api/order'
import { useUserStore } from '@/stores/user'

const route = useRoute()
const useStore = useUserStore()
const userInfo = useStore.userInfo
const payInfo = ref({})
const query = route.query

const payParams = computed(() => {
  const content = payInfo.value.displayContent || ''
  return content.split('||')
})

const serverUrl = computed(() => {
  if(payParams.value.length > 1) {
    return payParams.value[1]
  }
  return ''
})

const json = computed(() => {
  if(payParams.value.length > 2) {
    return payParams.value[2]
  }
  return ''
})

const signature = computed(() => {
  if(payParams.value.length) {
    return payParams.value[0]
  }
  return ''
})

const buildReturnUrl = () => {
  const ctxPath = import.meta.env.VITE_BASE_PATH_CONTEXT
  let redirect = `${ location.origin + ctxPath  }/#/order/paySuccess?no=${ query.no }&oType=${ query.oType || '' }`
  redirect = encodeURIComponent(redirect)
  const url = `${ import.meta.env.VITE_BASE_PAY_API  }/common/front-call-back?url=${ redirect }`
  return url
}

const submitPayment = async (payOrderId: any, channelCode: any) => {
  if(!payOrderId || !channelCode) {
    showFailToast('必选参数为空')
    return
  }
  const params = {
    id: payOrderId,
    channelCode: channelCode,
    returnUrl: buildReturnUrl()
  }
  if(channelCode === 'axinfu_web') {
    params.channelExtras = {
      userNo: userInfo.userNo,
      userName: userInfo.name,
      clientType: 'h5'
    }
  }
  console.log('params====', params)
  try {
    showLoadingToast('支付处理中...')
    const res = await submitPayOrder(params)
    payInfo.value = res
    setTimeout(() => {
      document.getElementById('submit_form')?.submit()
    }, 500)
  } catch(e) {
    showFailToast("支付处理失败")
  }
}

defineExpose({
  submitPayment
})

</script>

<style>

</style>