<template>
  <div v-if="!showLoginPage" class="login-container">
    <div class="login-bg"></div>
    <div class="login">
      <img :src="logo" class="size-110 logo" />
      <div class="mt-10 color-#000 text-20 lh-24 font-700">{{configData.title}}</div>
      <div class="mt-15 color-#999 text-14 lh-20">一站领齐·便捷高效·正品保障</div>
      <div class="btn mb-40 mt-40" @click="login(1)">统一身份认证</div>
      <div class="btn" @click="login(2)">附属单位用户</div>
    </div>
  </div>
  <login-page v-if="showLoginPage" @backLoginFirst="loginPageBack" :sourceTag="loginPageSource"></login-page>
</template>

<script setup lang="ts">
import { nextTick,ref} from 'vue'
import { useRoute,useRouter } from 'vue-router';

import logo from '@/assets/login/logo.png'
import { useHomeStore } from '@/stores/home'
import { toLogin } from '@/utils/ssoUtils'

import loginPage from './login.vue'

const router = useRouter()
const route = useRoute()
const redirectPath = router.currentRoute.value.query.redirect || ''

const homeStore = useHomeStore()
const configData = homeStore.configInfo.configData

const showLoginPage = ref(false)
const loginPageSource = ref('index')

const loginPageBack = () => {
  if(configData.loginType === 1) {
    router.go(-1)
    return
  }
  showLoginPage.value = false
}

const login = (num: number) => {
  if(num === 1) {
    toLogin(2, redirectPath)
    return
  }
  showLoginPage.value = true
}

nextTick(() => {
  if(configData.loginType === 1) {
    loginPageSource.value = ''
    showLoginPage.value = true
    return
  }
  showLoginPage.value = route.query.m === '1'
})

</script>

<style scoped lang="scss">
.login-container {
  background: #fdf4ef;
  height: 100vh;
  .login-bg {
    background: linear-gradient(180deg, $primary-color 0%, #fdf4ef 100%);
    height: 351px;
    &:after {
      content: '';
      display: block;
      width: 100%;
      height: 100%;
      background: url('@/assets/login/login-bg.png') no-repeat;
      background-size: 375px 229px;
    }
  }
}
.login {
  background: #ffffff;
  border-radius: 15px;
  //margin: 138px 15px 0;
  padding: 65px 32px;
  position: absolute;
  z-index: 10;
  width: 345px;
  top: 157px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;

  .logo {
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
  }
  .btn {
    width: 280px;
    height: 50px;
    line-height: 50px;
    background: linear-gradient( 180deg, #FF836B 0%, #CF4D32 100%);
    border-radius: 25px;
    font-size: 16px;
    color: #FFFFFF;
  }
}
</style>
