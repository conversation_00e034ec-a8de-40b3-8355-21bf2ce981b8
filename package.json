{"name": "vite", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "dev:test": "vite --mode test", "build-check": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build:prod": "vite build --mode production", "build:pre": "vite build --mode pre", "build:wdfl": "vite build --mode wdfl", "build": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@vant/area-data": "^1.5.1", "@vueuse/core": "^10.9.0", "address-parse": "^1.2.19", "axios": "^1.6.5", "dayjs": "^1.11.11", "js-cookie": "^3.0.5", "pinia": "^2.1.7", "qs": "^6.12.1", "sass": "^1.69.7", "sm-crypto": "^0.3.13", "vant": "^4.8.7", "vconsole": "^3.15.0", "vue": "^3.4.21", "vue-router": "^4.2.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^18.19.3", "@types/qs": "^6.9.15", "@types/sm-crypto": "^0.3.4", "@unocss/preset-rem-to-px": "^0.58.3", "@vitejs/plugin-vue": "^4.5.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.0", "eslint": "^8.49.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-vue": "^9.17.0", "npm-run-all2": "^6.1.1", "postcss-px-to-viewport-8-plugin": "^1.2.5", "prettier": "^3.0.3", "typescript": "~5.3.0", "unocss": "^0.58.3", "vite": "^4.4.7", "vite-plugin-html": "^3.2.2", "vue-tsc": "^1.8.25"}}