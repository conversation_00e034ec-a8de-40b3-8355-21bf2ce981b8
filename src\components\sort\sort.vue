<template>
  <div class="caret-wrapper" :class="{ ascending, descending }" @click.stop="handleClick">
    <i class="sort-caret ascending"></i>
    <i class="sort-caret descending"></i>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'Sort'
})

defineProps({
  descending: {
    type: Boolean,
    default: false
  },
  ascending: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['change'])

const handleClick = () => {
  emits('change')
}
</script>

<style scoped lang="scss">
.caret-wrapper {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  height: 14px;
  font-size: 0;
  vertical-align: middle;
  cursor: pointer;
  overflow: initial;
  position: relative;
  &.ascending .sort-caret.ascending {
    border-bottom-color: $primary-color;
  }
  &.descending .sort-caret.descending {
    border-top-color: $primary-color;
  }
  .sort-caret {
    width: 0;
    height: 0;
    border: solid 5px transparent;
    position: absolute;
    left: 7px;
  }
  .ascending {
    border-bottom-color: #d9d9d9;
    top: -5px;
  }
  .descending {
    border-top-color: #d9d9d9;
    bottom: -3px;
  }
}
</style>
