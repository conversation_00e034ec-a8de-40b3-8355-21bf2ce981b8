<template>
  <div class="position-relative">
    <NavBar title="意见反馈" />
    <div class="ml-15 mr-15">
      <van-field
        v-model="message"
        class="mt-10 mb-15"
        rows="8"
        autosize
        type="textarea"
        maxlength="200"
        placeholder="您的宝贵意见将帮助我们不断进步..."
        show-word-limit
      />
      <van-button type="danger" block round @click="submit">提交反馈</van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import NavBar from '@/components/navbar/index.vue'
import { showToast } from 'vant'
import { userFeedback } from '@/api/my'
import { useRouter } from 'vue-router'

const message = ref('')

const router = useRouter()

const submit = () => {
  if (!message.value) {
    showToast('请输入反馈内容')
    return
  }
  userFeedback({
    content: message.value
  }).then((res) => {
    showToast('提交成功')
    router.go(-1)
  })
}
</script>

<style scoped lang="scss"></style>
