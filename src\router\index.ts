import { createRouter, createWebHashHistory } from 'vue-router'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '',
      redirect: '/home'
    },
    {
      path: '/mall/sso-link',
      component: () => import('../views/login/ssolink.vue')
    },
    {
      path: '/login',
      redirect: '/login/index',
      component: () => import('../layout/index.vue'),
      children: [
        {
          path: 'index',
          name: 'loginIndex',
          component: () => import('../views/login/index.vue')
        },
        {
          path: 'login',
          name: 'login',
          component: () => import('../views/login/login.vue')
        },
        {
          path: 'findPassword',
          name: 'FindPassword',
          component: () => import('../views/login/findPassword.vue')
        },
      ]
    },
    {
      path: '/default',
      component: () => import('../views/default/default.vue')
    },
    {
      path: '/',
      component: () => import('../layout/index.vue'),
      children: [
        {
          path: 'home',
          name: 'Home',
          component: () => import('../views/home/<USER>')
        },
        {
          path: 'classify',
          name: 'Classify',
          component: () => import('../views/classify/index.vue')
        },
        {
          path: 'shoppingCart',
          name: 'ShoppingCart',
          component: () => import('../views/shoppingCart/index.vue')
        },
        {
          path: 'mine',
          name: 'Mine',
          component: () => import('../views/mine/index.vue')
        }
      ]
    },
    {
      path: '/order',
      component: () => import('../layout/index.vue'),
      children: [
        {
          path: 'index',
          name: 'OrderIndex',
          component: () => import('../views/order/index.vue')
        },
        {
          path: 'orderConfirm',
          name: 'orderConfirm',
          component: () => import('../views/order/orderConfirm.vue')
        },
        {
          path: 'orderDetail',
          name: 'orderDetail',
          component: () => import('../views/order/orderDetail.vue')
        },
        {
          path: 'orderSuccess',
          name: 'orderSuccess',
          component: () => import('../views/order/orderSuccess.vue')
        },
        {
          path: 'paySuccess',
          name: 'PaySuccess',
          component: () => import('../views/order/paySuccess.vue')
        },
        {
          path: 'logistics',
          name: 'Logistics',
          component: () => import('../views/order/logistics.vue')
        },
        {
          path: 'pay',
          name: 'Pay',
          component: () => import('../views/order/pay.vue')
        }
      ]
    },
    {
      path: '/sales',
      component: () => import('../layout/index.vue'),
      children: [
        {
          path: 'after',
          name: 'AfterSales',
          component: () => import('../views/sales/after.vue')
        },
        {
          path: 'inspect',
          name: 'AfterInspect',
          component: () => import('../views/sales/inspect.vue')
        },
        {
          path: 'apply',
          name: 'SalesApply',
          component: () => import('../views/sales/apply.vue'),
          meta: {
            title: '申请售后'
          }
        },
        {
          path: 'delivery-info',
          name: 'DeliveryInfo',
          component: () => import('../views/sales/deliveryInfo.vue'),
          meta: {
            title: '补充发货信息'
          }
        }
      ]
    },
    {
      path: '/address',
      component: () => import('../layout/index.vue'),
      children: [
        {
          path: 'index',
          name: 'Address',
          component: () => import('../views/mine/address/index.vue')
        },
        {
          path: 'add',
          name: 'AddAddress',
          component: () => import('../views/mine/address/add.vue')
        }
      ]
    },
    {
      path: '/classify',
      component: () => import('../layout/index.vue'),
      children: [
        {
          path: 'filter',
          name: 'ClassifyFilter',
          component: () => import('../views/classify/filter.vue')
        }
      ]
    },
    {
      path: '/customerService',
      component: () => import('../layout/index.vue'),
      children: [
        {
          path: 'index',
          name: 'CustomerService',
          component: () => import('../views/mine/customerService.vue')
        }
      ]
    },
    {
      path: '/points',
      component: () => import('../layout/index.vue'),
      children: [
        {
          path: 'index',
          name: 'Points',
          component: () => import('../views/points/index.vue')
        }
      ]
    },
    {
      path: '/home',
      component: () => import('../layout/index.vue'),
      children: [
        {
          path: 'searchHistory',
          name: 'HomeSearchHistory',
          component: () => import('../views/home/<USER>')
        },
        {
          path: 'search',
          name: 'HomeSearch',
          component: () => import('../views/home/<USER>')
        },
        {
          path: 'collectionZone',
          name: 'CollectionZone',
          component: () => import('../views/home/<USER>')
        }
      ]
    },
    {
      path: '/collect',
      component: () => import('../layout/index.vue'),
      children: [
        {
          path: 'index',
          name: 'Collect',
          component: () => import('../views/mine/collect.vue')
        }
      ]
    },
    {
      path: '/feedback',
      component: () => import('../layout/index.vue'),
      children: [
        {
          path: 'index',
          name: 'Feedback',
          component: () => import('../views/mine/feedback.vue')
        }
      ]
    },
    {
      path: '/profile',
      component: () => import('../layout/index.vue'),
      children: [
        {
          path: 'password',
          name: 'password',
          component: () => import('../views/mine/password.vue')
        }
      ]
    },
    {
      path: '/goods',
      component: () => import('../layout/index.vue'),
      children: [
        {
          path: 'detail',
          name: 'GoodsDetail',
          component: () => import('../views/goods/detail.vue')
        }
      ]
    },
    {
      path: '/comment',
      component: () => import('../layout/index.vue'),
      children: [
        {
          path: 'index',
          name: 'CommentIndex',
          component: () => import('../views/comment/index.vue')
        },
        {
          path: 'create',
          name: 'CommentCreate',
          component: () => import('../views/comment/create.vue')
        }
      ]
    },
    {
      path: '/notice',
      component: () => import('../layout/index.vue'),
      children: [
        {
          path: 'index',
          name: 'NoticeIndex',
          component: () => import('../views/notice/index.vue')
        },
        {
          path: 'detail',
          name: 'NoticeDetail',
          component: () => import('../views/notice/detail.vue')
        }
      ]
    },
  ]
})

export default router
