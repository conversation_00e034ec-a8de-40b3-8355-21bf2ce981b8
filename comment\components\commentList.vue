<template>
    <div>
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list v-if="list.length" v-model:loading="loading" loading-text="加载中..." :finished="finished"
                finished-text="没有更多了" @load="onLoad" :immediate-check="false">
                <div class="p-10 bg-white rounded-10 mx-10" :style="{ marginTop: idx !== 0 ? '10px' : '' }"
                    v-for="(child, idx) in list" :key="idx">
                    <Comment :comment="child" @refresh="getData" />
                </div>
            </van-list>
            <EmptyBox v-else />
        </van-pull-refresh>
    </div>
</template>

<script setup lang="ts">
import { getProductCommentPage, like } from '@/api/comment'

defineOptions({
    name: 'CommentList'
})
import { closeToast, showLoadingToast } from 'vant'
import { onMounted, reactive, ref } from 'vue'

import EmptyBox from '@/components/emptyBox/index.vue'
import Comment from '@/views/comment/components/comment.vue'

const props = defineProps({
  skuId: {
    type: [String, Number],
    default: ''
  }
})

const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)

const page = reactive({
    current: 1,
    pageSize: 10,
    total: 0
})

const list = ref([])

const getData = () => {
    console.log(1)
    loading.value = true
    showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0
    })
    getProductCommentPage({
        pageNo: page.current,
        pageSize: page.pageSize,
        skuId: props.skuId
    }).then((res: any) => {
        if (refreshing.value) {
            list.value = []
            refreshing.value = false
        }
        loading.value = false
        if (page.current === 1) {
            list.value = res?.list || []
        } else {
            list.value = list.value.concat(res.list)
        }
        page.total = res?.total || 0
        finished.value = list.value.length >= page.total
    }).catch(() => {
        loading.value = false
        finished.value = true
    }).finally(() => {
        closeToast()
    })
}

const onLoad = () => {
    page.current++
    getData()
}

const onRefresh = () => {
    page.current = 1
    // 清空列表数据
    finished.value = false

    // 重新加载数据
    // 将 loading 设置为 true，表示处于加载状态
    loading.value = true
    getData()
}

onMounted(() => {
    getData()
})
</script>

<style scoped lang="scss"></style>