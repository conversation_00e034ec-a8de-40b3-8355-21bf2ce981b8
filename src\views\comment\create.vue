<template>
    <div>
        <van-sticky>
            <NavBar title="商品评价" />
        </van-sticky>

        <van-cell-group inset title="商品信息">
            <van-cell class="product-info mb-10">
                <div class="flex items-center">
                    <van-image :src="orderItem?.imageUrl" class="product-img w-80 h-80 fit-contain" />
                    <div class="product-details flex flex-col justify-between h-full ml-10">
                        <div class="order-no text-left">{{ route.query.no }}</div>
                        <div class="product-name text-left">{{ orderItem?.skuName }}</div>
                    </div>
                </div>
            </van-cell>
        </van-cell-group>

        <van-cell-group inset title="填写评价">
            <!-- 商品评分 -->
            <div class="round ">
                <div class="flex flex-row ml-16 mt-16 ml-16 mb-16">
                    <span class="mr-2 w-75">商品评分</span>
                    <van-rate v-model="rating" />
                </div>
            </div>

            <!-- 评价内容 -->
            <div class="round">
                <div class="flex flex-row ml-16 mt-16 mb-16">
                    <span class="mr-16 w-75">评价内容</span>
                    <textarea v-model="reviewContent" id="reviewContent" name="reviewContent" rows="5"
                        placeholder="写下您的评价..."
                        class="text-left w-full mr-8 border-none outline-none placeholder-gray-300"></textarea>
                </div>
            </div>


            <!-- 匿名选择 -->
            <div class="round">
                <div class="flex items-center ml-16 mt-16 mb-16">
                    <span class="mr-2  w-75">匿名评价</span>
                    <van-switch v-model="isAnonymous" size="14" />
                </div>
            </div>

            <!-- 图片上传 -->
            <div class="upload-image flex items-center flex-col justify-between h-full ml-10">
                <van-uploader class="mt-10 ml-6" multiple :max-count="5" :before-read="handleBeforeRead"
                    :after-read="handleAfterRead" v-model="fileList" />
                <span class="mt-10 mb-10 text-gray-500 text-5xl">图片大小5M以内，最多只能上传 5 张图片</span>
            </div>
        </van-cell-group>

        <!-- 提交按钮 -->
        <div class="submit-btn mt-30 ml-10 mr-10">
            <van-button round block type="primary" native-type="submit" @click="submitReview">提交评价</van-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'

defineOptions({
    name: 'CommentCreate'
})
import { onMounted, ref } from 'vue'

import NavBar from '@/components/navbar/index.vue'
import { Rate, showFailToast, showSuccessToast } from 'vant';
import { orderCancel, orderDetail, Item } from '@/api/order'
import { uploadFile, createComment } from '@/api/comment'

const active = ref()
const route = useRoute()
const router = useRouter()

const props = defineProps({
    no: {
        type: [String, Number],
        default: ''
    },
    skuId: {
        type: [String, Number],
        default: ''
    }
})

// 评分、评价内容、匿名状态和上传图片状态
const rating = ref(0);
const reviewContent = ref('');
const isAnonymous = ref(true);
const fileList = ref([]);
const orderId = ref(0);
const orderItem = ref<Item>({
    imageUrl: '',
    skuName: '',
    commented: false,
    count: 0,
    id: 0,
    picUrl: '',
    skuId: 0,
    skuPrice: 0,
    skuTotalPrice: 0
});

const getOrderDetail = () => {
    orderDetail({
        orderNo: route.query.no
    }).then((res) => {
        var skuInfoList = res.skuInfoList || []
        orderId.value = res.id || 0
        for (let i = 0; i < skuInfoList.length; i++) {
            const item = skuInfoList[i];
            if (item.skuId == route.query.skuId) {
                orderItem.value = item
                return;
            }
        }
    }).finally(() => {
    })
}

getOrderDetail()

const handleBeforeRead = (files) => {
    files = Array.isArray(files) ? files : [files];
    files.forEach((file) => {
        // Check file size
        if (file.size > 5 * 1024 * 1024) {
            showFailToast('图片大小不能超过5M')
            return false;
        }
        if (file.type !== 'image/jpeg' && file.type !== 'image/png') {
            showFailToast('请上传 jpg/png格式图片')
            return false;
        }
    });
    return true;
};

// 处理图片上传后的操作
const handleAfterRead = async (files) => {
    // Use Promise.all to handle concurrent uploads
    files = Array.isArray(files) ? files : [files];
    await Promise.all(
        files.map(async (file) => {
            const res = await uploadFile(file.file);
            if (res != null) {
                // Update the file's URL upon successful upload
                file.url = res;
            }
        })
    );
};

// 提交评价
const submitReview = async () => {
    if (orderId.value == 0 || route.query == null || route.query.no == null) {
        showFailToast('订单信息获取失败')
        return
    }
    if (rating.value == 0) {
        showFailToast('请评分')
        return
    }
    if (reviewContent.value == null || reviewContent.value.length == 0) {
        showFailToast('请填写评价内容')
        return
    }
    if (fileList.value.length > 0 && fileList.value.length != fileList.value.map(file => file.url).length) {
        showFailToast('有图片未上传成功，请重试')
        return
    }
    var params = {
        skuId: route.query.skuId,
        orderNo: route.query.no,
        score: rating.value, // 评价 1-5
        content: reviewContent.value, // 评价内容
        anonymousFlag: isAnonymous.value ? 1 : 0,  // 是否匿名 1-是 0 否
    }
    if (fileList.value.length > 0) {
        params.pics = fileList.value.map(file => file.url).join(',')
    }
    const res = await createComment(params)
    if (res != null) {
        router.back()
        showSuccessToast('评价提交成功')
    }
    else {
        showFailToast('评价提交失败')
    }
}

onMounted(() => {

})
</script>

<style scoped lang="scss"></style>