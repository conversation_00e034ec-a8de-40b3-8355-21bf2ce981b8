<template>
  <div>
    <van-sticky>
      <NavBar :title="title" />
    </van-sticky>
    <div class="p-10 rounded-10 bg-white page">
      <div class="flex items-center text-[#999] mb-10">
        <van-icon name="clock-o" size="12" />
        <span class="ml-6 lh-17 text-13">{{ getDate(detailInfo.publishTime) }}</span>
      </div>

      <div v-html="detailInfo.content" class="rich-body"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'

import { contentDetail } from '@/api/notice'
import NavBar from '@/components/navbar/index.vue'
import {getDate} from '@/utils/base'

defineOptions({
  name: 'NoticeDetail'
})

const detailInfo = ref({})
const route = useRoute()
const title = ref('')

const getDetail = () => {
  contentDetail(route.query.id).then((res) => {
    detailInfo.value = res || {}
    title.value = detailInfo.value.title || '内容详情'
  })
}

onMounted(() => {
  getDetail()
})
</script>

<style scoped lang="scss">
.page {
  min-height: calc(100vh - 46px);
  font-size: 1.1em;
}
</style>
<style lang="scss">
.rich-body {
  img {
    width: 100%;
  }
}
</style>
