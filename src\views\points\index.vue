<template>
  <div class="position-relative">
    <NavBar title="我的积分"></NavBar>
    <div class="flex items-center justify-between pl-24 pr-24">
      <div v-for="item in pointType" class="color-#000 text-14 w-84 h-20 lh-20" :class="{'active': active === item.val}" @click="clickTab(item)">
        {{ item.title }}
      </div>
    </div>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="bg-white rounded-10 mt-10">
      <van-list
        v-if="list.length"
        v-model:loading="loading"
        loading-text="加载中..."
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        :immediate-check="false"
      >
        <div class="ml-20 mr-20 h-80 flex items-center justify-between active-item" v-for="(item, index) in list" :key="item.id || index">
          <div class="flex items-center">
            <img class="size-36 mr-10" :src="getImg()" alt="">
            <div>
              <div class="lh-17 font-600 text-14 text-black mb-6">
                {{ getTitle(item) }}
              </div>
              <div class="lh-17 text-10 color-#999">{{ formatDateTime(item.createTime) }}</div>
            </div>
          </div>
          <div class="text-right">
            <div v-if="active === 0" class="lh-17 text-14 text-black mb-6">{{ item.rechargeAmount }}</div>
            <div v-if="active === 1" class="lh-17 text-14 text-black mb-6">-{{ item.useAmount }}</div>
            <div v-if="active === 2" class="lh-17 text-14 text-black mb-6">+{{ item.useAmount }}</div>
            <div v-if="active === 0" class="lh-17 text-10 color-#999">余额：{{ item.remainAmount }}</div>
            <div v-if="active === 1 || active === 2" class="lh-17 text-10 color-#999">订单号：{{ item.orderNo }}</div>
          </div>
        </div>
      </van-list>
      <EmptyBox v-else />
    </van-pull-refresh>
  </div>
</template>

<script setup lang="ts">
import { closeToast,showLoadingToast } from 'vant'
import { onMounted,reactive, ref } from 'vue'

import { recharge,usagePage } from '@/api/user'
import consume from '@/assets/points/consume.png'
import rechargePng from '@/assets/points/recharge.png'
import refund from '@/assets/points/refund.png'
import EmptyBox from '@/components/emptyBox/index.vue'
import NavBar from '@/components/navbar/index.vue'
import { formatDateTime } from "@/utils/orderUtils"

const active = ref(0)

const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);

const onLoad = () => {
  pageIndex.value++
  getList()
}

const onRefresh = () => {
  pageIndex.value = 1
  // 清空列表数据
  finished.value = false

  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true
  getList()
}

const pageSize = ref(20)
const pageIndex = ref(1)
const list = ref([] as any)

const pointType = reactive([
  {
    title: '积分充值记录',
    val: 0
  },
  {
    title: '积分消费记录',
    val: 1
  },
  {
    title: '积分退款记录',
    val: 2
  }
])

const clickTab = (item: any) => {
  active.value = item.val
  onRefresh()
}

const getList = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0
  });
  loading.value = true
  let url: any
  const params = {
    pageNo: pageIndex.value,
    pageSize: pageSize.value,
  }
  if (active.value === 0) {
    url = recharge
  } else {
    url = usagePage
    params.useType = active.value
  }
  const data = await url(params)
  closeToast()
  if (refreshing.value) {
    list.value = []
    refreshing.value = false
  }
  loading.value = false
  if (pageIndex.value === 1) {
    list.value = data?.list || []
  } else {
    list.value = list.value.concat(data.list)
  }
  finished.value = list.value.length >= data.total
}

const getImg = () => {
  if (active.value === 0) {
    return rechargePng
  } else if (active.value === 1) {
    return consume
  } else if (active.value === 2) {
    return refund
  }
}

const getTitle = (item?: any) => {
  const scoreTypeText = item?.scoreType === 0 ? '福利积分' : '扶贫积分'
  
  if (active.value === 0) {
    return `${scoreTypeText}充值成功`
  } else if (active.value === 1) {
    return `${scoreTypeText}付款成功`
  } else if (active.value === 2) {
    return `${scoreTypeText}退款成功`
  }
}

onMounted(() => {
  clickTab(pointType[0])
})
</script>

<style scoped lang="scss">
.active {
  color: $primary-color;
  font-size: 17px;
  font-weight: 600;
  width: 102px;
}
.active-item {
  border-bottom: 1px solid #F2F2F2;
}
</style>
