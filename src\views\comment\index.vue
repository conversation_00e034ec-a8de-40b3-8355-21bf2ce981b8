<template>
    <div>
        <van-sticky>
            <NavBar title="商品评价"/>
        </van-sticky>
        <CommentList :skuId="route.query.skuId" style="height: calc(100vh - 82px); overflow: auto" />
    </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'

defineOptions({
    name: 'CommentIndex'
})
import { onMounted, ref } from 'vue'

import NavBar from '@/components/navbar/index.vue'
import CommentList from '@/views/comment/components/commentList.vue'

const active = ref()
const route = useRoute()
const router = useRouter()

onMounted(() => {

})
</script>

<style scoped lang="scss"></style>