<template>
  <div class="login-container">
    <div class="login-bg"></div>
    <div class="login">
      <img :src="logo" class="size-110 logo" />
      <div class="text-16 font-600 mb-30 text-center" style="color: #3c3b3b">用户认证</div>
      <div>
<!--        <div class="mt-10 flex" v-if="numFlag == 2">-->
<!--          <img :src="unit" class="size-20 mr-5" />-->
<!--          <div class="color-#333 font-600">附属单位：</div>-->
<!--        </div>-->
<!--        <van-field v-if="numFlag == 2" class="formInput" v-model="form.unitValue" is-link readonly placeholder="请选择附属单位" @click="showPicker = true" />-->
<!--        <div class="mt-10 flex">-->
<!--          <img :src="card" class="size-20 mr-5" />-->
<!--          <div class="color-#333 font-600">工号：</div>-->
<!--        </div>-->
<!--        <van-field class="formInput" v-model="form.card" placeholder="请输入工号" :border="false" />-->
        <div class="mt-10 flex">
          <img :src="phone" class="size-20 mr-5" />
          <div class="color-#333 font-600">手机号：</div>
        </div>
        <van-field class="formInput" v-model.trim="form.phone" placeholder="请输入手机号" :border="false">
          <template #button>
            <van-button size="small" :disabled="sendDisabled" @click="sendCode" class="!text-black !text-12 !font-400 sendCode">{{ sendText }}</van-button>
          </template>
        </van-field>
        <div class="mt-10 flex">
          <img :src="smcode" class="size-20 mr-5" />
          <div class="color-#333 font-600">验证码：</div>
        </div>
        <van-field class="formInput" v-model.trim="form.verifyCode" :maxlength="6" placeholder="请输入验证码" :border="false"></van-field>
        <div class="mt-10 flex">
          <img :src="password" class="size-20 mr-5" />
          <div class="color-#333 font-600">密码：</div>
        </div>
        <van-field
          class="formInput mt-20 mb-20"
          v-model="form.newPassword"
          :type="passwordType"
          placeholder="请输入密码"
          :border="false"
        >
          <template #right-icon>
            <van-icon
              :name="passwordType === 'password' ? 'closed-eye' : 'eye-o'"
              @click="passwordType = passwordType === 'password' ? 'text' : 'password'"
            />
          </template>
        </van-field>
        <div class="mt-10 flex">
          <img :src="checkpassword" class="size-20 mr-5" />
          <div class="color-#333 font-600">确认密码：</div>
        </div>
        <van-field
          class="formInput mt-20 mb-20"
          v-model="form.checkPassword"
          :type="checkPasswordType"
          placeholder="请输入确认密码"
          :border="false"
        >
          <template #right-icon>
            <van-icon
              :name="checkPasswordType === 'password' ? 'closed-eye' : 'eye-o'"
              @click="checkPasswordType = checkPasswordType === 'password' ? 'text' : 'password'"
            />
          </template>
        </van-field>
        <div class="flex gap-25">
          <van-button color="#D9D9D9" size="small" block type="default" @click="backLogin">
            返回
          </van-button>
          <van-button class="btn-primary" size="small" block type="default" :disabled="!form.phone || !form.newPassword || !form.checkPassword" @click="login">
            保存
          </van-button>
        </div>
      </div>

    </div>
    <van-popup v-model:show="showPicker" position="bottom">
      <van-picker
        :columns="columns"
        @confirm="onConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { showToast } from 'vant'

defineOptions({
  name: 'FindPassword'
})
import { computed, reactive, ref } from 'vue'
import { useRouter } from 'vue-router';

import {resetPassword, sendSmsCode} from '@/api/login'
import checkpassword from '@/assets/login/checkpassword.png'
import logo from '@/assets/login/logo.png'
import password from '@/assets/login/password.png'
import phone from '@/assets/login/phone.png'
import smcode from '@/assets/login/smcode.png'
import { encryptSm2 } from '@/utils/auth'

const form = reactive({
  phone: '',
  verifyCode: '',
  newPassword: '',
  checkPassword: ''
})

const showPicker = ref(false)
const columns = ref([
  { text: '武汉工会大学', value: '1' },
  { text: '附属学校', value: '2' },
  { text: '劳动服务公司', value: '3' },
  { text: '人民医院', value: '4' },
  { text: '中南医院', value: '5' },
])
const passwordType = ref('password')
const checkPasswordType = ref('password')

const router = useRouter();
const login = () => {
  if (!form.phone) {
    showToast('请输入手机号')
    return
  }
  if(!/^1[3-9]\d{9}$/.test(form.phone)) {
    showToast('手机号格式不正确')
    return
  }
  if (!form.verifyCode) {
    showToast('请输入验证码')
    return
  }
  if (!/^\d{4,6}$/.test(form.verifyCode)) {
    showToast("验证码格式不正确");
    return
  }
  if (!form.newPassword) {
    showToast('请输入密码')
    return
  }
  if (!form.checkPassword) {
    showToast('请输入确认密码')
    return
  }
  // 密码要求大于8位小于20位,必须包含大小写字母数字及符号
  if (!/^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,20}$/.test(form.newPassword)) {
    showToast('密码要求长度8位至20位，必须包含数字、字母及特殊字符')
    return
  }
  if (form.newPassword !== form.checkPassword) {
    showToast('两次密码不一致')
    return
  }
  resetPassword({
    mobile: form.phone,
    code: form.verifyCode,
    password: encryptSm2(form.newPassword),
    crypto: 'sm2'
  }).then(() => {
    showToast('密码设置成功')
    router.push('/login/index');
  })
}

const backLogin = () => {
  router.push('/login');
}

const initSeconds = ref(61)
const smsSeconds = ref(61)

const sendDisabled = computed(() => smsSeconds.value < initSeconds.value)

const sendText = computed(() => {
  if(smsSeconds.value === initSeconds.value) {
    return '发送验证码'
  }
  return `剩余${ smsSeconds.value }秒`
})

const sendCode = () => {
  // 校验手机号
  if (!/^1[3456789]\d{9}$/.test(form.phone)) {
    showToast('请输入正确的手机号')
    return
  }

  // 发送短信验证码...
  const params = {
    mobile: form.phone,
    scene: 3
  }
  sendSmsCode(params).then(data => {
    showToast('短信验证码发送成功')

    const func = () => {
      if (smsSeconds.value === 0) {
        smsSeconds.value = initSeconds.value
        return
      }
      smsSeconds.value--
      setTimeout(func, 1000)
    }
    smsSeconds.value--
    setTimeout(func, 1000)
  }).catch(e => {
    smsSeconds.value = initSeconds.value
  })
}

const onConfirm = ({ selectedOptions }: any) => {
  form.unitValue = selectedOptions[0]?.text;
  showPicker.value = false;
};
</script>

<style scoped lang="scss">
.login-container {
  background: #fdf4ef;
  height: 100vh;
  .login-bg {
    background: linear-gradient(180deg, $primary-color 0%, #fdf4ef 100%);
    height: 351px;
    &:after {
      content: '';
      display: block;
      width: 100%;
      height: 100%;
      background: url('@/assets/login/login-bg.png') no-repeat;
      background-size: 375px 229px;
    }
  }
}
.login {
  background: #ffffff;
  border-radius: 15px;
  padding: 70px 30px 20px;
  position: absolute;
  z-index: 10;
  width: 345px;
  top: 142px;
  left: 50%;
  transform: translateX(-50%);

  .logo {
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
  }

  .formInput {
    height: 36px;
    display: flex;
    align-items: center;
    margin-top: 5px;
    padding: 0 10px;
    background: #fff;
    border: 1px solid #D9D9D9;
    border-radius: 4px;
    :deep(.van-field__control) {
      &::placeholder {
        color: #afafaf;
      }
    }
  }

  :deep(.van-button--default) {
    border: none;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    height: 36px;
    line-height: 36px;
  }
}

.sendCode {
  :deep(.van-button__text) {
    color: $primary-color;
  }
}
</style>
