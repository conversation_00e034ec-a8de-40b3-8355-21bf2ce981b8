<template>
  <div class="setOverFlowHidden">
    <van-sticky>
      <NavBar @onSearch="onSearch" @onCancel="onCancel" class="!bg-white">
        <template #title>
          {{ title }}
        </template>
        <template #right>
          <img :src="search" class="size-20" />
        </template>
      </NavBar>
    </van-sticky>
    <Goods ref="goodSearch" class="mt-10" :searchVal="searchVal"/>
  </div>
</template>

<script setup lang="ts">
import { nextTick,ref } from 'vue'
import { useRoute } from 'vue-router'

import search from '@/assets/shoppingCart/search.png'
import NavBar from "@/components/navbar/searchBar.vue"
import Goods from '@/views/goods/index.vue'

defineOptions({
  name: 'CollectionZone'
})
const route = useRoute()
const title = ref(route.query.name)
const goodSearch = ref()

const searchVal = ref('')
const onSearch = (val: string) => {
  searchVal.value = val
  goodSearch.value.search()
}

const onCancel = () => {
  searchVal.value = ''
}

nextTick(() => {
  goodSearch.value.search()
})

</script>

<style scoped lang="scss"></style>
