<template>
  <div class="overflow-hidden">
    <NavBar ref="navBarRef" @onSearch="onSearch" :searchPage="searchPage">
      <template #title>
        {{title}}
      </template>
    </NavBar>
    <Goods ref="goodSearch" :searchVal="searchVal"/>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'HomeSearch'
})
import { nextTick, onActivated, ref } from 'vue'
import { useRoute } from 'vue-router'

import NavBar from '@/components/navbar/searchBar.vue'
import { useHomeStore } from '@/stores/home'
import Goods from '@/views/goods/index.vue'

const searchPage = ref(true)
const searchVal = ref('')
const route = useRoute()
const navBarRef = ref()
const title = ref('')
const homeStore = useHomeStore()
const goodSearch = ref()
const queryRand = ref('')

const initState = () => {
  title.value = '搜索专区'
  searchVal.value = ''
  searchPage.value = true
  const supplierId = route.query.supplierId
  if (supplierId) {
    searchPage.value = false

    const supplier = homeStore.configInfo.supplierList.find(item => item.id === parseInt(supplierId))
    if(supplier) {
      title.value = supplier.name
    }
  }

  if (route.query.keyword) {
    searchVal.value = route.query.keyword as string
    searchPage.value = false
    nextTick(() => {
      navBarRef.value.value = searchVal.value
    })
  }
  goodSearch.value.search()
}

const onSearch = (val: string) => {
  searchVal.value = val
  goodSearch.value.search()
}

onActivated(() => {
  if(route.query.rand !== queryRand.value) {
    initState()
    queryRand.value = route.query.rand
  }
})

</script>

<style scoped lang="scss"></style>
