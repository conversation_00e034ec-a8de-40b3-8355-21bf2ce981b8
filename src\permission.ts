import router from '@/router'
import { useHomeStore } from '@/stores/home'
import { useUserStore } from '@/stores/user'
import { getToken, removeToken } from '@/utils/auth'
import { toLogin } from '@/utils/ssoUtils'

const handleRefreshState = (to, from) => {
  let state = false
  if((['Home', 'HomeSearch'].includes(from.name))&& to.name === 'HomeSearch') {
    state = true
  } else if((['Home'].includes(from.name)) && to.name === 'CollectionZone') {
    state = true
  } else if((['Classify'].includes(from.name)) && to.name === 'ClassifyFilter') {
    state = true
  }

  const useHome = useHomeStore()
  if(state) {
    useHome.updateCacheKey()
  }
}

const entryStatus = {
  status: 0
}

const handleExtraEntry = (from, to) => {
  if(entryStatus.status === 1) {
    return
  }

  const clt = to && to.query && to.query.clt === '1'
  if(!clt) {
    return
  }
  console.log('clt-------', clt)
  entryStatus.status === 1
  // 清空本地token
  removeToken()
}

router.beforeEach(async (to, from, next) => {
  handleRefreshState(to, from)
  const whiteList = ['/login/index',
    '/login/login',
    '/login/findPassword',
    '/home',
    '/classify',
    '/classify/filter',
    '/goods/detail',
    '/comment/index',
    '/mall/sso-link',
    '/default',
    '/home/<USER>',
    '/home/<USER>']
  const userStore = useUserStore()
  const userInfo = userStore.userInfo || {}
  const useHome = useHomeStore()
  if (!useHome.configInfo.configData.inited && to.path !== '/default') {
    const configResult = await useHome.getConfigData()
    if(!configResult) {
      next({ path: '/default' })
      return
    }
    await useHome.loadHomeConfig()
    userStore.loadUserAddress()
  }

  if(to.path === '/default') {
    next()
    return
  }

  handleExtraEntry(from, to)

  if (getToken() && (!userInfo || !userInfo.nickname)) {
    await userStore.loadUserInfo()
    userStore.loadCartCount()
    userStore.loadUserAddress()
  }

  if (getToken()) {
    if (to.path === '/login/index') {
      next({ path: '/' })
    } else {
      next()
    }
  } else if (whiteList.includes(to.path)) {
    next()
  } else {
    toLogin(1, to.fullPath)
    next()
  }
})

router.afterEach(() => {})
