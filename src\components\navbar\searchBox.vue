<template>
  <van-sticky>
    <div class="search-bar pt-8 pb-10 pl-8 !bg-[#F2F2F2] position-relative z-200">
      <van-search
        class="search"
        v-model="value"
        show-action
        :left-icon="search"
        placeholder="搜索商品"
        @search="onSearch"
      >
        <template #action>
          <van-button type="danger" size="mini" round @click="onSearch">搜索</van-button>
        </template>
      </van-search>
      <div class="flex items-center px-8 text-16 flex-shrink-0">
        <span @click="$router.go(-1)">取消</span>
      </div>
    </div>
  </van-sticky>
</template>

<script setup lang="ts">
import search from '@/assets/home/<USER>'
import { ref } from 'vue'

defineOptions({
  name: 'SearchBox'
})

const value = ref('')
const emits = defineEmits(['search'])

const onSearch = (val: string) => {
  emits('search', value.value)
}
</script>

<style scoped lang="scss">
:deep(.van-sticky--fixed) {
  z-index: 200;
}
.search-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .search {
    position: relative;
    border-radius: 20px;
    padding: 0 4px 0 12px;
    width: 100%;
    height: 32px;
  }
  :deep(.van-search__content) {
    background-color: #fff;
    padding-left: 0;
    border-radius: 20px;
  }
  :deep(.van-search__action) {
    position: absolute;
    right: 8px;
    top: -5px;
    width: 50px;
    padding: 0;
    button {
      width: 100%;
    }
  }
  :deep(.van-icon-search:before) {
    color: #c8c8c8;
  }
}
</style>
